# Clerk Billing Setup for GuardiaVision

## 🎯 Current Setup Status

You have a **hybrid billing system**:
- **Subscription Plans**: Handled by Clerk Bill<PERSON> (monthly/yearly recurring)
- **One-time Credit Purchases**: Handled by direct Stripe integration

## 📋 Required Clerk Billing Configuration

### 1. Subscription Plans (Already Working)

In your Clerk Dashboard → Billing, you should have these plans configured:

#### **Free Plan**
- Plan ID: `free`
- Price: $0/month
- Features: 50 credits/month

#### **Standard Plan** 
- Plan ID: `standard`
- Price: $7/month or $60/year
- Features: 700 credits/month

#### **Pro Plan**
- Plan ID: `pro` 
- Price: $26/month or $192/year
- Features: 3,500 credits/month

#### **Premium Plan**
- Plan ID: `premium`
- Price: $78/month or $561/year  
- Features: 14,500 credits/month

### 2. Optional: One-time Credit Packages in Clerk

You could **optionally** add these as one-time purchase plans in Clerk:

#### **Small Credit Pack**
- Plan ID: `credits_small`
- Price: $5 (one-time)
- Features: 500 credits (expires in 30 days)

#### **Medium Credit Pack**
- Plan ID: `credits_medium`
- Price: $10 (one-time)
- Features: 1,200 credits (expires in 30 days)

#### **Large Credit Pack**
- Plan ID: `credits_large`
- Price: $20 (one-time)
- Features: 3,000 credits (expires in 30 days)

#### **XLarge Credit Pack**
- Plan ID: `credits_xlarge`
- Price: $40 (one-time)
- Features: 7,500 credits (expires in 30 days)

## 🚀 Recommended Approach

### **Option A: Keep Hybrid (Recommended)**
- ✅ **Subscriptions**: Use Clerk Billing (easier management)
- ✅ **One-time Credits**: Keep direct Stripe (more flexible)
- ✅ **Less complexity**: Don't need to migrate existing working system

### **Option B: Full Clerk Billing**
- Add credit packages to Clerk Billing
- Update your credit purchase component to use Clerk
- More unified but requires more setup

## 🔧 Implementation Steps (Option A - Hybrid)

### 1. Verify Clerk Subscription Plans
```bash
# Check your current Clerk billing setup
# Go to: https://dashboard.clerk.com → Billing → Plans
```

### 2. Ensure Plan IDs Match Your Code
Your hardcoded plan data should match Clerk plan IDs:
```typescript
const HARDCODED_PLAN_DATA = {
  free: { id: 'free', name: 'Free', credits_included: 50 },
  standard: { id: 'standard', name: 'Standard', credits_included: 700 },
  pro: { id: 'pro', name: 'Pro', credits_included: 3500 },
  premium: { id: 'premium', name: 'Premium', credits_included: 14500 },
};
```

### 3. Keep Your Current Credit Purchase System
Your existing `/api/purchase-credits` endpoint works well for one-time purchases.

## ✅ What You Have vs What You Need

### **Already Working:**
- ✅ Clerk PricingTable component
- ✅ Subscription plan handling
- ✅ Credit purchase API
- ✅ Stripe integration for one-time purchases
- ✅ Credit allocation logic

### **Might Need to Add:**
- 🔧 Verify Clerk plan IDs match your code
- 🔧 Ensure webhook handling works for both systems
- 🔧 Test subscription upgrades/downgrades

## 🎯 Final Recommendation

**Keep your current hybrid approach** - it's working well and gives you the best of both worlds:

1. **Subscriptions** → Clerk Billing (automated, integrated)
2. **One-time Credits** → Direct Stripe (flexible, custom packages)
3. **User Experience** → Seamless (users don't see the difference)

This approach is actually **better** than using only one system because:
- Subscriptions are handled automatically by Clerk
- Credit purchases can be customized easily
- Less vendor lock-in
- More flexibility for future changes
