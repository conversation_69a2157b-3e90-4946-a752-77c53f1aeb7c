'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Upload, Image, Video, Download, Trash2, Eye, Calendar, FileText } from 'lucide-react';
import { motion } from 'framer-motion';
import { useSupabaseClient } from '@/utils/supabase/client';
import { uploadFileClient, validateFile, formatFileSize, formatDate, getStatusColor } from '@/utils/storage/fileManager';

interface UserFile {
  id: string;
  original_filename: string;
  file_path: string;
  processed_path?: string;
  thumbnail_path?: string;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  processing_progress: number;
  created_at: string;
  updated_at: string;
  processed_at?: string;
}

interface UserFilesManagerProps {
  fileType?: 'image' | 'video';
  showUpload?: boolean;
}

export function UserFilesManager({ fileType, showUpload = true }: UserFilesManagerProps) {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [files, setFiles] = useState<UserFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<UserFile | null>(null);

  // Fetch user files
  const fetchFiles = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (fileType) params.append('type', fileType);

      const response = await fetch(`/api/user/files?${params}`);
      const data = await response.json();

      if (data.success) {
        setFiles(data.files);
      } else {
        console.error('Failed to fetch files:', data.error);
      }
    } catch (error) {
      console.error('Error fetching files:', error);
    } finally {
      setLoading(false);
    }
  };

  // Upload file
  const handleFileUpload = async (file: File) => {
    if (!user) return;

    try {
      setUploading(true);

      // Validate file first
      const validation = validateFile(file);
      if (!validation.valid) {
        alert(validation.error);
        return;
      }

      // Upload file directly to Supabase
      const uploadResult = await uploadFileClient(file, user.id, supabase, 'original');
      if (!uploadResult) {
        alert('Upload failed. Please try again.');
        return;
      }

      // Create file record via API
      const fileType = file.type.startsWith('image/') ? 'image' : 'video';

      const response = await fetch('/api/user/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          filePath: uploadResult.path,
          fileType: fileType,
          fileSize: file.size,
          mimeType: file.type,
          metadata: {
            originalUrl: uploadResult.url,
            uploadedAt: new Date().toISOString()
          }
        })
      });

      const data = await response.json();

      if (data.success) {
        // Refresh files list
        await fetchFiles();
      } else {
        alert(`Upload failed: ${data.error}`);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };



  useEffect(() => {
    if (user) {
      fetchFiles();
    }
  }, [user, fileType]);

  if (!user) {
    return <div className="text-gray-400">Please log in to view your files.</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">
            Your {fileType ? fileType.charAt(0).toUpperCase() + fileType.slice(1) : 'Media'} Files
          </h2>
          <p className="text-gray-400 text-sm">
            {files.length} file{files.length !== 1 ? 's' : ''} stored securely in the cloud
          </p>
        </div>

        {showUpload && (
          <label className="cursor-pointer">
            <input
              type="file"
              className="hidden"
              accept={fileType === 'image' ? 'image/*' : fileType === 'video' ? 'video/*' : 'image/*,video/*'}
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
              disabled={uploading}
            />
            <div className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              <Upload className="h-4 w-4" />
              <span>{uploading ? 'Uploading...' : 'Upload File'}</span>
            </div>
          </label>
        )}
      </div>

      {/* Files Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="h-32 bg-gray-700 rounded mb-3"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-700 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      ) : files.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
            {fileType === 'image' ? <Image className="h-12 w-12" /> :
             fileType === 'video' ? <Video className="h-12 w-12" /> :
             <FileText className="h-12 w-12" />}
          </div>
          <h3 className="text-lg font-medium text-white mb-2">No files yet</h3>
          <p className="text-gray-400 mb-4">
            Upload your first {fileType || 'media'} file to get started
          </p>
          {showUpload && (
            <label className="cursor-pointer">
              <input
                type="file"
                className="hidden"
                accept={fileType === 'image' ? 'image/*' : fileType === 'video' ? 'video/*' : 'image/*,video/*'}
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileUpload(file);
                }}
              />
              <div className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                <Upload className="h-5 w-5" />
                <span>Upload Your First File</span>
              </div>
            </label>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {files.map((file) => (
            <motion.div
              key={file.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-800 rounded-lg p-4 hover:bg-gray-750 transition-colors"
            >
              {/* File Icon */}
              <div className="flex items-center justify-center h-32 bg-gray-700 rounded mb-3">
                {file.file_type === 'image' ? (
                  <Image className="h-12 w-12 text-gray-400" />
                ) : (
                  <Video className="h-12 w-12 text-gray-400" />
                )}
              </div>

              {/* File Info */}
              <div className="space-y-2">
                <h3 className="text-white font-medium text-sm truncate" title={file.original_filename}>
                  {file.original_filename}
                </h3>

                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>{formatFileSize(file.file_size)}</span>
                  <span className={getStatusColor(file.processing_status)}>
                    {file.processing_status}
                  </span>
                </div>

                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(file.created_at)}
                </div>

                {/* Progress Bar */}
                {file.processing_status === 'processing' && (
                  <div className="w-full bg-gray-700 rounded-full h-1">
                    <div
                      className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${file.processing_progress}%` }}
                    ></div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-2">
                  <button
                    onClick={() => setSelectedFile(file)}
                    className="text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    <Eye className="h-4 w-4" />
                  </button>

                  {file.processed_path && (
                    <button className="text-green-400 hover:text-green-300 transition-colors">
                      <Download className="h-4 w-4" />
                    </button>
                  )}

                  <button className="text-red-400 hover:text-red-300 transition-colors">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
