'use client';

import { useState } from 'react';
import { <PERSON>ert<PERSON><PERSON>gle, Zap, ArrowRight, X, Crown, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface LowCreditsWarningProps {
  credits: number;
  subscriptionType: string;
  onClose?: () => void;
}

export function LowCreditsWarning({ credits, subscriptionType, onClose }: LowCreditsWarningProps) {
  const [isVisible, setIsVisible] = useState(true);

  // Only show warning if credits are below 20
  if (credits >= 20 || !isVisible) {
    return null;
  }

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) onClose();
  };

  const handleUpgrade = () => {
    window.location.href = '/dashboard/billing';
  };

  const handleContactTeam = () => {
    window.location.href = 'mailto:<EMAIL>?subject=Enterprise Credits Request&body=Hi GuardiaVision Team,%0A%0AI need more credits for my projects. Please contact me to discuss enterprise options.%0A%0AThank you!';
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md mx-4"
        >
          <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-2xl border border-orange-400/30 overflow-hidden">
            {/* Header */}
            <div className="p-4 bg-black/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-lg">Low Credits Alert!</h3>
                    <p className="text-orange-100 text-sm">
                      Only {credits} credits remaining
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleClose}
                  className="text-white/70 hover:text-white transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-4 bg-white/10 backdrop-blur-sm">
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-3">
                  <Zap className="h-8 w-8 text-yellow-300" />
                </div>
                <p className="text-white text-sm mb-4">
                  You're running low on credits! Upgrade your plan to continue processing your images and videos without interruption.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {/* Upgrade Plan Button */}
                <button
                  onClick={handleUpgrade}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center group shadow-lg"
                >
                  <Crown className="h-5 w-5 mr-2 group-hover:animate-bounce" />
                  Upgrade Your Plan
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>

                {/* Contact Team Button */}
                <button
                  onClick={handleContactTeam}
                  className="w-full bg-white/20 hover:bg-white/30 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center group border border-white/30"
                >
                  <Star className="h-4 w-4 mr-2" />
                  Need More? Contact Our Team
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>

              {/* Quick Stats */}
              <div className="mt-4 pt-3 border-t border-white/20">
                <div className="flex justify-between text-xs text-white/80">
                  <span>Current Plan: {subscriptionType}</span>
                  <span>Credits Left: {credits}</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
