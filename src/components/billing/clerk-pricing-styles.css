/* Custom styles for Professional Pricing Component */

.styled-clerk-pricing {
  @apply space-y-8 min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(120, 119, 198, 0.08) 0%, transparent 50%);
}

.pricing-header {
  @apply text-center mb-12 relative;
}

.pricing-header::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #8b5cf6, #06b6d4);
  border-radius: 2px;
}

/* Enhanced feature cards */
.feature-card {
  @apply bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-300;
  background-image: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(6, 182, 212, 0.05) 100%);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.1);
}

.feature-icon {
  @apply w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center mb-4;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* Support section styling */
.support-section {
  @apply relative;
}

.support-section .bg-navy-light {
  @apply bg-gray-800/60 backdrop-blur-sm border border-gray-700/50;
  background-image: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(6, 182, 212, 0.08) 100%);
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.5); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #8b5cf6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button styles */
.btn-gradient-primary {
  @apply bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-gradient-secondary {
  @apply bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .styled-clerk-pricing {
    @apply px-4;
  }

  .pricing-header h1 {
    @apply text-3xl;
  }

  .feature-card {
    @apply p-4;
  }
}

/* Individual pricing card styling */
.clerk-pricing-wrapper [data-clerk-pricing-card] {
  @apply bg-navy border border-navy-light rounded-xl p-6 shadow-xl transition-all duration-300 hover:shadow-2xl hover:border-green-400/30;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Popular plan highlight */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-popular="true"] {
  @apply border-2 border-green-400 relative;
}

.clerk-pricing-wrapper [data-clerk-pricing-card][data-popular="true"]::before {
  content: "Most Popular";
  @apply absolute -top-4 left-0 right-0 mx-auto w-32 rounded-md bg-green-400 py-1.5 text-center text-sm font-bold text-navy;
}

/* Plan name styling */
.clerk-pricing-wrapper [data-clerk-plan-name] {
  @apply text-xl font-bold text-white mb-2;
}

/* Plan description */
.clerk-pricing-wrapper [data-clerk-plan-description] {
  @apply text-sm text-gray-400 mb-4;
}

/* Price styling */
.clerk-pricing-wrapper [data-clerk-price] {
  @apply text-4xl font-extrabold text-white mb-1;
}

.clerk-pricing-wrapper [data-clerk-price-period] {
  @apply text-sm text-gray-400;
}

/* Subscribe button styling */
.clerk-pricing-wrapper [data-clerk-subscribe-button] {
  @apply w-full h-12 rounded-lg font-medium transition-all duration-300 text-lg mt-6;
}

/* Free plan button */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="free"] [data-clerk-subscribe-button] {
  @apply border border-blue-400 text-blue-400 hover:text-white hover:border-transparent hover:bg-blue-500;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Standard plan button */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="standard"] [data-clerk-subscribe-button] {
  @apply border border-teal-400 text-teal-400 hover:text-white hover:border-transparent hover:bg-teal-500;
  box-shadow: 0 0 20px rgba(20, 184, 166, 0.3);
}

/* Pro plan button (popular) */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="pro"] [data-clerk-subscribe-button] {
  @apply bg-green-400 text-navy hover:bg-green-500;
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
}

/* Premium plan button */
.clerk-pricing-wrapper [data-clerk-pricing-card][data-plan="premium"] [data-clerk-subscribe-button] {
  @apply border border-purple-400 text-purple-400 hover:text-white hover:border-transparent hover:bg-purple-500;
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

/* Features list styling */
.clerk-pricing-wrapper [data-clerk-features] {
  @apply mt-6 space-y-3;
}

.clerk-pricing-wrapper [data-clerk-feature] {
  @apply flex items-start text-sm text-gray-300;
}

.clerk-pricing-wrapper [data-clerk-feature-icon] {
  @apply flex-shrink-0 rounded-full bg-green-400 p-1 mt-0.5 mr-3;
}

.clerk-pricing-wrapper [data-clerk-feature-icon] svg {
  @apply h-3 w-3 text-navy;
}

/* Toggle styling for billing period */
.clerk-pricing-wrapper [data-clerk-billing-toggle] {
  @apply flex items-center justify-center mb-8 space-x-4;
}

.clerk-pricing-wrapper [data-clerk-billing-toggle] button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-300;
}

.clerk-pricing-wrapper [data-clerk-billing-toggle] button[data-active="true"] {
  @apply bg-green-400 text-navy;
}

.clerk-pricing-wrapper [data-clerk-billing-toggle] button[data-active="false"] {
  @apply text-gray-400 hover:text-white;
}

/* Feature cards styling */
.feature-card {
  @apply bg-navy-light rounded-lg p-6 border border-navy-light hover:border-green-400/30 transition-all duration-300;
}

.feature-icon {
  @apply w-12 h-12 bg-navy rounded-lg flex items-center justify-center mb-4;
}

/* Support section */
.support-section {
  @apply max-w-2xl mx-auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .clerk-pricing-wrapper [data-clerk-pricing-table] {
    @apply grid-cols-1 gap-4;
  }

  .features-section .grid {
    @apply grid-cols-1 gap-6;
  }
}

/* Animation for cards */
.clerk-pricing-wrapper [data-clerk-pricing-card] {
  animation: fadeInUp 0.6s ease-out forwards;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(1) {
  animation-delay: 0.1s;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(2) {
  animation-delay: 0.2s;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(3) {
  animation-delay: 0.3s;
}

.clerk-pricing-wrapper [data-clerk-pricing-card]:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
