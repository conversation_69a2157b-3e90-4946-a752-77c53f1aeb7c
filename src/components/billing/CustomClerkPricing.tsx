'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Check, HelpCircle, Crown } from 'lucide-react';
import { motion } from 'framer-motion';

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  credits: number;
  features: string[];
  isPopular?: boolean;
  buttonVariant: 'blue' | 'green' | 'purple' | 'teal' | 'gold';
  clerkPlanId: string; // This should match your Clerk plan IDs
  isContactSales?: boolean;
}

const plans: PricingPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for trying out our service',
    price: { monthly: 0, yearly: 0 },
    credits: 5,
    features: [
      'One-time 5 credits offer',
      'Basic AI anonymization',
      'Basic editor tools',
      'Blur preview',
      'Create your workspace'
    ],
    buttonVariant: 'blue',
    clerkPlanId: 'free'
  },
  {
    id: 'standard',
    name: 'Standard',
    description: 'For bloggers and casual users',
    price: { monthly: 1, yearly: 12 },
    credits: 700,
    features: [
      '700 credits per month',
      'Full anonymization for people and vehicles',
      'Save work files',
      'Batch upload and download',
      '1080p (Full HD) support'
    ],
    buttonVariant: 'teal',
    clerkPlanId: 'standard'
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'For business and professional users',
    price: { monthly: 26, yearly: 312 },
    credits: 3500,
    features: [
      '3,500 credits per month',
      'Full anonymization for people and vehicles',
      'Save work files',
      'Batch upload and download',
      '4K support',
      'Unlimited blur/pixel subjects'
    ],
    isPopular: true,
    buttonVariant: 'green',
    clerkPlanId: 'pro'
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'For video production businesses',
    price: { monthly: 78, yearly: 936 },
    credits: 14500,
    features: [
      '14,500 credits per month',
      'Full anonymization for people and vehicles',
      'Save work files',
      'Batch upload and download',
      '4K support',
      'Unlimited blur/pixel subjects',
      'Premium concierge support'
    ],
    buttonVariant: 'purple',
    clerkPlanId: 'premium'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large teams and custom needs',
    price: { monthly: 0, yearly: 0 },
    credits: 0,
    features: [
      'Unlimited credits',
      'Custom processing speeds',
      'Dedicated account manager',
      'Custom integrations',
      'On-premise deployment',
      'SLA guarantees',
      'White-label options',
      '24/7 phone support'
    ],
    buttonVariant: 'gold',
    clerkPlanId: 'enterprise',
    isContactSales: true
  }
];

export function CustomClerkPricing() {
  const [isYearly, setIsYearly] = useState(true);
  const [loading, setLoading] = useState<string | null>(null);
  const { user } = useUser();

  const handleSubscribe = async (plan: PricingPlan) => {
    if (plan.id === 'free') {
      // Handle free plan - redirect to dashboard
      window.location.href = '/dashboard';
      return;
    }

    if (plan.isContactSales) {
      // Handle contact sales for enterprise plan
      const subject = encodeURIComponent('Enterprise Plan Inquiry');
      const body = encodeURIComponent(`Hi GuardiaVision Team,

I'm interested in the Enterprise plan for my organization. Please contact me to discuss:

- Custom credit requirements
- Pricing for our team size
- Integration needs
- Support requirements

Current user: ${user?.emailAddresses[0]?.emailAddress || 'N/A'}

Thank you!`);

      window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
      return;
    }

    setLoading(plan.id);

    try {
      // Call our subscription API
      const response = await fetch('/api/clerk/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: plan.clerkPlanId,
          isYearly,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Show success message
        alert(`🎉 Successfully selected ${plan.name} plan (${isYearly ? 'yearly' : 'monthly'})!\n\nRedirecting to dashboard...`);

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to select plan');
      }

    } catch (err) {
      console.error('Subscription error:', err);

      // Show error message
      alert(`❌ Failed to select ${plan.name} plan.\n\nPlease try again or contact support.`);
    } finally {
      setLoading(null);
    }
  };

  const buttonClasses = {
    blue: "blue-button-glow block w-full h-12 rounded-lg border border-blue-400 px-4 py-3 text-center font-medium text-blue-400 hover:text-white hover:border-transparent hover:bg-blue-500 transition-all duration-300 text-lg flex items-center justify-center",
    green: "green-button-glow block w-full h-12 rounded-lg bg-green-400 px-4 py-3 text-center font-medium text-navy hover:bg-green-500 transition-all duration-300 shadow-lg text-lg flex items-center justify-center",
    purple: "purple-button-glow block w-full h-12 rounded-lg border border-purple-400 px-4 py-3 text-center font-medium text-purple-400 hover:text-white hover:border-transparent hover:bg-purple-500 transition-all duration-300 text-lg flex items-center justify-center",
    teal: "teal-button-glow block w-full h-12 rounded-lg border border-teal-400 px-4 py-3 text-center font-medium text-teal-400 hover:text-white hover:border-transparent hover:bg-teal-500 transition-all duration-300 text-lg flex items-center justify-center",
    gold: "gold-button-glow block w-full h-12 rounded-lg bg-gradient-to-r from-yellow-400 to-orange-500 px-4 py-3 text-center font-medium text-navy hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 shadow-lg text-lg flex items-center justify-center"
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-4">Choose Your Plan</h1>
        <p className="text-gray-300 mb-8">
          Welcome {user?.firstName}! Upgrade your account to unlock more credits and advanced features.
        </p>
      </div>

      {/* Billing Toggle */}
      <div className="flex items-center justify-center mb-8">
        <div className="bg-navy-light rounded-lg p-1 flex">
          <button
            onClick={() => setIsYearly(false)}
            className={`px-4 py-2 rounded-md font-medium transition-all duration-300 ${
              !isYearly ? 'bg-green-400 text-navy' : 'text-gray-400 hover:text-white'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setIsYearly(true)}
            className={`px-4 py-2 rounded-md font-medium transition-all duration-300 ${
              isYearly ? 'bg-green-400 text-navy' : 'text-gray-400 hover:text-white'
            }`}
          >
            Yearly
            <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded">Save 40%</span>
          </button>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`relative rounded-xl h-full flex flex-col ${
              plan.isPopular ? 'border-2 border-green-400' : 'border border-navy-light'
            } bg-navy p-6 shadow-xl transition-all duration-300 hover:shadow-2xl hover:border-green-400/30`}
          >
            {plan.isPopular && (
              <div className="absolute -top-4 left-0 right-0 mx-auto w-44 rounded-md bg-green-400 py-1.5 text-center text-sm font-bold text-navy shadow-lg flex items-center justify-center">
                <Crown className="w-4 h-4 mr-1" />
                Most Popular Plan
              </div>
            )}

            <h3 className="text-xl font-bold text-white">{plan.name}</h3>
            <p className="mt-1 text-sm text-gray-400">{plan.description}</p>

            <div className="mt-4">
              {plan.isContactSales ? (
                <div className="flex items-baseline">
                  <span className="text-4xl font-extrabold text-white">Custom</span>
                  <span className="ml-1 text-sm text-gray-400">Pricing</span>
                </div>
              ) : (
                <div className="flex items-baseline">
                  <span className="text-sm text-gray-400">€</span>
                  <span className="text-4xl font-extrabold text-white">
                    {isYearly ? Math.round(plan.price.yearly / 12) : plan.price.monthly}
                  </span>
                  <span className="ml-1 text-sm text-gray-400">/ Month</span>
                </div>
              )}
              {isYearly && plan.price.yearly > 0 && !plan.isContactSales && (
                <div className="mt-1 text-xs text-gray-400">
                  €{plan.price.yearly} per year
                </div>
              )}
            </div>

            <button
              onClick={() => handleSubscribe(plan)}
              disabled={loading === plan.id}
              className={`${buttonClasses[plan.buttonVariant]} mt-6 ${
                loading === plan.id ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {loading === plan.id ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : plan.id === 'free' ? (
                'Start for Free'
              ) : plan.isContactSales ? (
                'Contact Our Team'
              ) : (
                `Subscribe to ${plan.name}`
              )}
            </button>

            <ul className="mt-6 space-y-3 flex-grow">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-start">
                  <div className="flex-shrink-0 rounded-full bg-green-400 p-1 mt-0.5">
                    <Check className="h-3 w-3 text-navy" />
                  </div>
                  <span className="ml-3 text-sm text-gray-300">{feature}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
