'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Check, Crown, Zap, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import './clerk-pricing-styles.css';

// Pricing plans data
const pricingPlans = [
  {
    id: 'free',
    name: 'Free',
    price: { monthly: 0, yearly: 0 },
    credits: '50 credits',
    description: 'Perfect for trying out GuardiaVision',
    features: [
      '50 credits per month',
      'Standard processing speed',
      'Community support',
      'Basic blur effects',
      'HD quality exports'
    ],
    buttonText: 'Current Plan',
    buttonVariant: 'secondary' as const,
    popular: false,
  },
  {
    id: 'standard',
    name: 'Standard',
    price: { monthly: 7, yearly: 60 },
    credits: '700 credits',
    description: 'Great for regular users and small projects',
    features: [
      '700 credits per month',
      'Standard processing speed',
      'Email support',
      'All blur effects',
      'HD quality exports',
      'Batch processing'
    ],
    buttonText: 'Upgrade to Standard',
    buttonVariant: 'primary' as const,
    popular: false,
  },
  {
    id: 'pro',
    name: 'Pro',
    price: { monthly: 26, yearly: 192 },
    credits: '3,500 credits',
    description: 'Perfect for professionals and businesses',
    features: [
      '3,500 credits per month',
      'Priority processing speed',
      'Priority email support',
      'All blur effects',
      '4K quality exports',
      'Advanced batch processing',
      'API access',
      'Custom integrations'
    ],
    buttonText: 'Upgrade to Pro',
    buttonVariant: 'featured' as const,
    popular: true,
  },
  {
    id: 'premium',
    name: 'Premium',
    price: { monthly: 78, yearly: 561 },
    credits: '14,500 credits',
    description: 'For large teams and enterprise needs',
    features: [
      '14,500 credits per month',
      'Fastest processing speed',
      'Premium phone & email support',
      'All blur effects',
      '4K quality exports',
      'Advanced batch processing',
      'Full API access',
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantee'
    ],
    buttonText: 'Upgrade to Premium',
    buttonVariant: 'premium' as const,
    popular: false,
  },
];

export function StyledClerkPricing() {
  const { user } = useUser();
  const [isYearly, setIsYearly] = useState(true);
  const [loading, setLoading] = useState<string | null>(null);

  const handlePlanSelect = async (planId: string) => {
    if (planId === 'free') return;

    setLoading(planId);

    try {
      // Call the sync API to upgrade the user's plan
      const response = await fetch('/api/sync-clerk-subscription-hardcoded', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        // Show success message and redirect
        alert(`🎉 Successfully upgraded to ${planId} plan!\n\nRedirecting to dashboard...`);
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 2000);
      } else {
        throw new Error(result.error || 'Failed to upgrade plan');
      }
    } catch (error) {
      console.error('Error upgrading plan:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="styled-clerk-pricing">
      {/* Header */}
      <div className="pricing-header">
        <h1 className="text-4xl font-bold text-white mb-4">Choose Your Plan</h1>
        <p className="text-xl text-gray-300 mb-8">
          Welcome {user?.firstName}! Upgrade your account to unlock more credits and advanced features.
        </p>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center mb-12">
          <div className="bg-gray-800 rounded-lg p-1 flex">
            <button
              onClick={() => setIsYearly(false)}
              className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                !isYearly
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setIsYearly(true)}
              className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                isYearly
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Yearly
              <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded-full">
                Save 40%
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
        {pricingPlans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`relative rounded-2xl p-8 ${
              plan.popular
                ? 'bg-gradient-to-br from-purple-600 to-blue-600 ring-2 ring-purple-400 shadow-2xl scale-105'
                : 'bg-gray-800 border border-gray-700'
            }`}
          >
            {/* Popular Badge */}
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-1 rounded-full text-sm font-bold flex items-center">
                  <Crown className="h-4 w-4 mr-1" />
                  MOST POPULAR
                </div>
              </div>
            )}

            {/* Plan Header */}
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
              <p className="text-gray-300 text-sm mb-4">{plan.description}</p>

              {/* Price */}
              <div className="mb-4">
                <div className="flex items-baseline justify-center">
                  <span className="text-4xl font-bold text-white">
                    ${isYearly ? plan.price.yearly : plan.price.monthly}
                  </span>
                  <span className="text-gray-400 ml-2">
                    /{isYearly ? 'year' : 'month'}
                  </span>
                </div>
                {isYearly && plan.price.monthly > 0 && (
                  <p className="text-green-400 text-sm mt-1">
                    Save ${(plan.price.monthly * 12 - plan.price.yearly)} per year
                  </p>
                )}
              </div>

              {/* Credits */}
              <div className="bg-black/20 rounded-lg p-3 mb-6">
                <div className="flex items-center justify-center text-yellow-400">
                  <Zap className="h-5 w-5 mr-2" />
                  <span className="font-bold">{plan.credits}</span>
                </div>
                <p className="text-gray-300 text-xs mt-1">per month</p>
              </div>
            </div>

            {/* Features */}
            <ul className="space-y-3 mb-8">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-start">
                  <Check className="h-5 w-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">{feature}</span>
                </li>
              ))}
            </ul>

            {/* CTA Button */}
            <button
              onClick={() => handlePlanSelect(plan.id)}
              disabled={loading === plan.id || plan.id === 'free'}
              className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center ${
                plan.buttonVariant === 'featured'
                  ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-500 hover:to-orange-600 shadow-lg'
                  : plan.buttonVariant === 'premium'
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'
                  : plan.buttonVariant === 'primary'
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-600 text-gray-300 cursor-not-allowed'
              } ${loading === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {loading === plan.id ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
              ) : (
                <>
                  {plan.buttonText}
                  {plan.id !== 'free' && <ArrowRight className="h-4 w-4 ml-2" />}
                </>
              )}
            </button>
          </motion.div>
        ))}
      </div>

      {/* Additional Features Section */}
      <div className="features-section mt-12">
        <h2 className="text-2xl font-bold text-white mb-8 text-center">What's Included</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="feature-card">
            <div className="feature-icon">
              <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">AI-Powered Privacy</h3>
            <ul className="text-gray-300 space-y-2">
              <li>• Advanced face detection</li>
              <li>• Vehicle anonymization</li>
              <li>• Real-time processing</li>
              <li>• High accuracy rates</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">Fast Processing</h3>
            <ul className="text-gray-300 space-y-2">
              <li>• Batch processing</li>
              <li>• Cloud acceleration</li>
              <li>• Multiple formats</li>
              <li>• Quick turnaround</li>
            </ul>
          </div>

          <div className="feature-card">
            <div className="feature-icon">
              <svg className="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">Secure & Compliant</h3>
            <ul className="text-gray-300 space-y-2">
              <li>• GDPR compliant</li>
              <li>• End-to-end encryption</li>
              <li>• No data retention</li>
              <li>• Privacy by design</li>
            </ul>
          </div>
        </div>
      </div>



      {/* Support Section */}
      <div className="support-section mt-12 text-center">
        <div className="bg-navy-light rounded-lg p-8">
          <h3 className="text-xl font-bold text-white mb-4">Need Help Choosing?</h3>
          <p className="text-gray-300 mb-6">
            Our team is here to help you find the perfect plan for your needs.
          </p>
          <div className="flex gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center rounded-md bg-green-500 hover:bg-green-600 px-6 py-3 text-sm font-medium text-white transition-all duration-300"
            >
              Contact Support
            </a>
            <a
              href="/dashboard"
              className="inline-flex items-center rounded-md border border-gray-600 px-6 py-3 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-all duration-300"
            >
              Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
