'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Upload, Shield, Server } from 'lucide-react';

export function ServerSecureUpload() {
  const { user } = useUser();
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleFileUpload = async (file: File) => {
    if (!user) {
      setError('User not logged in');
      return;
    }

    try {
      setUploading(true);
      setError('');
      setMessage('Starting server-side secure upload...');

      console.log('🔍 User ID:', user.id);
      console.log('🔍 File:', file.name, file.size, file.type);

      // Upload via secure API route
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/secure-upload', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      if (data.success) {
        console.log('✅ Server-side upload successful:', data);
        setMessage(`✅ Secure upload complete! File ID: ${data.fileId}`);
      } else {
        throw new Error(data.error || 'Upload failed');
      }

    } catch (error: any) {
      console.error('❌ Server-side upload error:', error);
      setError(`Server-side upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  if (!user) {
    return (
      <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
        <p className="text-red-400">Please log in to test server-side secure upload</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-semibold mb-2 flex items-center">
          <Server className="h-5 w-5 mr-2 text-purple-400" />
          Server-Side Secure Upload
        </h3>
        <p className="text-gray-400 text-sm mb-4">
          This uploads files via server-side API using Clerk authentication and service role for storage.
          This approach bypasses JWT issues while maintaining security.
        </p>

        <label className="cursor-pointer">
          <input
            type="file"
            className="hidden"
            accept="image/*,video/*"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) handleFileUpload(file);
            }}
            disabled={uploading}
          />
          <div className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
            <Upload className="h-4 w-4" />
            <span>{uploading ? 'Uploading...' : 'Test Server Upload'}</span>
          </div>
        </label>
      </div>

      {/* Status Messages */}
      {message && (
        <div className="p-4 bg-purple-900/20 border border-purple-500 rounded-lg">
          <p className="text-purple-400">{message}</p>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      <div className="p-4 bg-gray-900 rounded-lg">
        <h4 className="text-white font-medium mb-2">Security Features:</h4>
        <ul className="text-gray-400 text-sm space-y-1">
          <li>✅ Clerk authentication on server</li>
          <li>✅ User isolation via folder structure</li>
          <li>✅ Service role for storage (bypasses JWT issues)</li>
          <li>✅ File validation and size limits</li>
          <li>✅ Database record creation</li>
          <li>✅ No client-side storage access</li>
        </ul>
      </div>
    </div>
  );
}
