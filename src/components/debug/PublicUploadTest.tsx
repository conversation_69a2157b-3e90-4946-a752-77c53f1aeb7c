'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Upload } from 'lucide-react';

export function PublicUploadTest() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleFileUpload = async (file: File) => {
    if (!user) {
      setError('User not logged in');
      return;
    }

    try {
      setUploading(true);
      setError('');
      setMessage('Starting public upload test...');

      console.log('🔍 User ID:', user.id);
      console.log('🔍 File:', file.name, file.size, file.type);

      // Upload to public thumbnails bucket (should work without complex policies)
      const timestamp = Date.now();
      const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filePath = `test/${timestamp}_${sanitizedFilename}`;

      console.log('🔍 Public upload path:', filePath);

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('thumbnails')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('❌ Public upload error:', uploadError);
        setError(`Public upload failed: ${uploadError.message}`);
        return;
      }

      console.log('✅ Public file uploaded successfully:', uploadData);
      setMessage('✅ Public upload successful! This means Supabase storage is working.');

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('thumbnails')
        .getPublicUrl(filePath);

      console.log('✅ Public file URL:', urlData.publicUrl);
      setMessage(`✅ Public upload complete! URL: ${urlData.publicUrl}`);

    } catch (error: any) {
      console.error('❌ Public upload error:', error);
      setError(`Public upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  if (!user) {
    return (
      <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
        <p className="text-red-400">Please log in to test public upload</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-semibold mb-2">Public Upload Test</h3>
        <p className="text-gray-400 text-sm mb-4">
          This tests upload to the public 'thumbnails' bucket to isolate auth issues.
        </p>

        <label className="cursor-pointer">
          <input
            type="file"
            className="hidden"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) handleFileUpload(file);
            }}
            disabled={uploading}
          />
          <div className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
            <Upload className="h-4 w-4" />
            <span>{uploading ? 'Uploading...' : 'Test Public Upload'}</span>
          </div>
        </label>
      </div>

      {/* Status Messages */}
      {message && (
        <div className="p-4 bg-green-900/20 border border-green-500 rounded-lg">
          <p className="text-green-400">{message}</p>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
}
