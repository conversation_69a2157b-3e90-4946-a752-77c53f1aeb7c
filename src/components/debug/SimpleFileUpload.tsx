'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Upload } from 'lucide-react';

export function SimpleFileUpload() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleFileUpload = async (file: File) => {
    if (!user) {
      setError('User not logged in');
      return;
    }

    try {
      setUploading(true);
      setError('');
      setMessage('Starting upload...');

      console.log('🔍 User ID:', user.id);
      console.log('🔍 File:', file.name, file.size, file.type);

      // Step 1: Test Supabase connection
      setMessage('Testing Supabase connection...');
      const { data: testData, error: testError } = await supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single();

      if (testError) {
        console.error('❌ Supabase connection error:', testError);
        setError(`Supabase connection failed: ${testError.message}`);
        return;
      }

      console.log('✅ Supabase connection successful');
      setMessage('Supabase connection successful. Uploading file...');

      // Step 2: Upload to storage
      const timestamp = Date.now();
      const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filePath = `${user.id}/uploads/${timestamp}_${sanitizedFilename}`;

      console.log('🔍 Upload path:', filePath);

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('user-uploads')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('❌ Upload error:', uploadError);
        setError(`Upload failed: ${uploadError.message}`);
        return;
      }

      console.log('✅ File uploaded successfully:', uploadData);
      setMessage('File uploaded successfully!');

      // Step 3: Get public URL
      const { data: urlData } = supabase.storage
        .from('user-uploads')
        .getPublicUrl(filePath);

      console.log('✅ File URL:', urlData.publicUrl);

      // Step 4: Test database insert
      setMessage('Creating database record...');

      const response = await fetch('/api/user/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          filePath: filePath,
          fileType: file.type.startsWith('image/') ? 'image' : 'video',
          fileSize: file.size,
          mimeType: file.type,
          metadata: {
            originalUrl: urlData.publicUrl,
            uploadedAt: new Date().toISOString()
          }
        })
      });

      const result = await response.json();

      if (!result.success) {
        console.error('❌ Database record creation failed:', result.error);
        setError(`Database record failed: ${result.error}`);
        return;
      }

      console.log('✅ Database record created:', result.fileId);
      setMessage(`✅ Upload complete! File ID: ${result.fileId}`);

    } catch (error: any) {
      console.error('❌ Upload error:', error);
      setError(`Upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  if (!user) {
    return (
      <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
        <p className="text-red-400">Please log in to test file upload</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-semibold mb-2">Simple File Upload Test</h3>
        <p className="text-gray-400 text-sm mb-4">
          User ID: {user.id}
        </p>

        <label className="cursor-pointer">
          <input
            type="file"
            className="hidden"
            accept="image/*,video/*"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) handleFileUpload(file);
            }}
            disabled={uploading}
          />
          <div className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
            <Upload className="h-4 w-4" />
            <span>{uploading ? 'Uploading...' : 'Upload Test File'}</span>
          </div>
        </label>
      </div>

      {/* Status Messages */}
      {message && (
        <div className="p-4 bg-blue-900/20 border border-blue-500 rounded-lg">
          <p className="text-blue-400">{message}</p>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Debug Info */}
      <div className="p-4 bg-gray-900 rounded-lg">
        <h4 className="text-white font-medium mb-2">Debug Checklist:</h4>
        <ul className="text-gray-400 text-sm space-y-1">
          <li>✅ User logged in: {user ? 'Yes' : 'No'}</li>
          <li>✅ User ID: {user?.id || 'N/A'}</li>
          <li>❓ Supabase buckets created: Check Storage tab</li>
          <li>❓ Database table created: Check Tables tab</li>
          <li>❓ RLS policies enabled: Check Authentication</li>
        </ul>
      </div>
    </div>
  );
}
