'use client';

import { useState } from 'react';
import { useUser, useSession } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { Upload, Shield, CheckCircle } from 'lucide-react';

export function SecureUploadTest() {
  const { user } = useUser();
  const { session } = useSession();
  const supabase = useSupabaseClient();
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [tokenInfo, setTokenInfo] = useState<any>(null);

  const checkTokenInfo = async () => {
    if (!session) return;
    
    try {
      const token = await session.getToken({ template: 'supabase' });
      if (token) {
        // Decode JWT to see claims (for debugging)
        const payload = JSON.parse(atob(token.split('.')[1]));
        setTokenInfo(payload);
        console.log('🔍 JWT Token payload:', payload);
      }
    } catch (error) {
      console.error('❌ Error getting token:', error);
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!user || !session) {
      setError('User not logged in or session not available');
      return;
    }

    try {
      setUploading(true);
      setError('');
      setMessage('Starting secure upload with Clerk JWT...');

      // Get Clerk token for Supabase
      const token = await session.getToken({ template: 'supabase' });
      if (!token) {
        setError('Failed to get Clerk JWT token');
        return;
      }

      console.log('🔍 User ID:', user.id);
      console.log('🔍 File:', file.name, file.size, file.type);
      console.log('🔍 JWT Token available:', !!token);

      // Upload with proper user folder structure
      const timestamp = Date.now();
      const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filePath = `${user.id}/uploads/${timestamp}_${sanitizedFilename}`;

      console.log('🔍 Secure upload path:', filePath);
      setMessage('Uploading with RLS security...');

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('user-uploads')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('❌ Secure upload error:', uploadError);
        setError(`Secure upload failed: ${uploadError.message}`);
        return;
      }

      console.log('✅ Secure file uploaded successfully:', uploadData);
      setMessage('✅ Secure upload successful! RLS policies working correctly.');

      // Test file access (should only see own files)
      setMessage('Testing RLS - checking file access...');
      
      const { data: listData, error: listError } = await supabase.storage
        .from('user-uploads')
        .list(user.id + '/uploads');

      if (listError) {
        console.error('❌ List error:', listError);
        setError(`File listing failed: ${listError.message}`);
        return;
      }

      console.log('✅ File listing successful (RLS working):', listData);
      setMessage(`✅ Secure upload complete! Found ${listData.length} files in your folder.`);

    } catch (error: any) {
      console.error('❌ Secure upload error:', error);
      setError(`Secure upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  if (!user) {
    return (
      <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
        <p className="text-red-400">Please log in to test secure upload</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-semibold mb-2 flex items-center">
          <Shield className="h-5 w-5 mr-2 text-green-400" />
          Secure Upload Test (RLS Enabled)
        </h3>
        <p className="text-gray-400 text-sm mb-4">
          This tests upload with proper Clerk JWT authentication and RLS security.
        </p>

        <div className="flex space-x-3 mb-4">
          <label className="cursor-pointer">
            <input
              type="file"
              className="hidden"
              accept="image/*,video/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
              disabled={uploading}
            />
            <div className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
              <Upload className="h-4 w-4" />
              <span>{uploading ? 'Uploading...' : 'Test Secure Upload'}</span>
            </div>
          </label>

          <button
            onClick={checkTokenInfo}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <CheckCircle className="h-4 w-4" />
            <span>Check JWT Token</span>
          </button>
        </div>
      </div>

      {/* Token Info */}
      {tokenInfo && (
        <div className="p-4 bg-blue-900/20 border border-blue-500 rounded-lg">
          <h4 className="text-blue-400 font-medium mb-2">JWT Token Info:</h4>
          <pre className="text-blue-300 text-xs overflow-auto">
            {JSON.stringify(tokenInfo, null, 2)}
          </pre>
        </div>
      )}

      {/* Status Messages */}
      {message && (
        <div className="p-4 bg-green-900/20 border border-green-500 rounded-lg">
          <p className="text-green-400">{message}</p>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      <div className="p-4 bg-gray-900 rounded-lg">
        <h4 className="text-white font-medium mb-2">Security Features:</h4>
        <ul className="text-gray-400 text-sm space-y-1">
          <li>✅ Row Level Security (RLS) enabled</li>
          <li>✅ User isolation via folder structure</li>
          <li>✅ Clerk JWT authentication</li>
          <li>✅ Proper access control policies</li>
          <li>✅ No service role bypass</li>
        </ul>
      </div>
    </div>
  );
}
