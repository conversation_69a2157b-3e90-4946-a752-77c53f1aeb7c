'use client';

import { useState } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Upload } from 'lucide-react';

// Create anonymous Supabase client (no auth)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

export function AnonymousUploadTest() {
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleFileUpload = async (file: File) => {
    try {
      setUploading(true);
      setError('');
      setMessage('Starting anonymous upload test...');

      console.log('🔍 File:', file.name, file.size, file.type);

      // Upload without authentication
      const timestamp = Date.now();
      const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filePath = `anonymous/${timestamp}_${sanitizedFilename}`;

      console.log('🔍 Anonymous upload path:', filePath);

      const { data: uploadData, error: uploadError } = await supabaseAnon.storage
        .from('thumbnails')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('❌ Anonymous upload error:', uploadError);
        setError(`Anonymous upload failed: ${uploadError.message}`);
        return;
      }

      console.log('✅ Anonymous file uploaded successfully:', uploadData);
      setMessage('✅ Anonymous upload successful! This means the bucket works without auth.');

      // Get public URL
      const { data: urlData } = supabaseAnon.storage
        .from('thumbnails')
        .getPublicUrl(filePath);

      console.log('✅ Anonymous file URL:', urlData.publicUrl);
      setMessage(`✅ Anonymous upload complete! URL: ${urlData.publicUrl}`);

    } catch (error: any) {
      console.error('❌ Anonymous upload error:', error);
      setError(`Anonymous upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-semibold mb-2">Anonymous Upload Test</h3>
        <p className="text-gray-400 text-sm mb-4">
          This tests upload without any authentication to isolate the Clerk/Supabase auth issue.
        </p>

        <label className="cursor-pointer">
          <input
            type="file"
            className="hidden"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) handleFileUpload(file);
            }}
            disabled={uploading}
          />
          <div className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
            <Upload className="h-4 w-4" />
            <span>{uploading ? 'Uploading...' : 'Test Anonymous Upload'}</span>
          </div>
        </label>
      </div>

      {/* Status Messages */}
      {message && (
        <div className="p-4 bg-purple-900/20 border border-purple-500 rounded-lg">
          <p className="text-purple-400">{message}</p>
        </div>
      )}

      {error && (
        <div className="p-4 bg-red-900/20 border border-red-500 rounded-lg">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      <div className="p-4 bg-gray-900 rounded-lg">
        <h4 className="text-white font-medium mb-2">What This Tests:</h4>
        <ul className="text-gray-400 text-sm space-y-1">
          <li>✅ Supabase connection works</li>
          <li>✅ Storage bucket exists and is accessible</li>
          <li>✅ File upload mechanics work</li>
          <li>❌ Isolates Clerk authentication issues</li>
        </ul>
      </div>
    </div>
  );
}
