import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

// Server-side file management utilities

// Delete file from storage
export async function deleteFile(bucket: string, path: string): Promise<boolean> {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);
    
    if (error) {
      console.error('Delete file error:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Delete file error:', error);
    return false;
  }
}

// Create file record in database
export async function createFileRecord(
  userId: string,
  filename: string,
  filePath: string,
  fileType: 'image' | 'video',
  fileSize: number,
  mimeType: string,
  metadata: any = {}
): Promise<string | null> {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    const { data, error } = await supabase
      .from('user_files')
      .insert({
        user_id: userId,
        original_filename: filename,
        file_path: filePath,
        file_type: fileType,
        file_size: fileSize,
        mime_type: mimeType,
        metadata: metadata,
        processing_status: 'pending'
      })
      .select('id')
      .single();
    
    if (error) {
      console.error('Create file record error:', error);
      return null;
    }
    
    return data.id;
  } catch (error) {
    console.error('Create file record error:', error);
    return null;
  }
}

// Update file processing status
export async function updateFileProcessingStatus(
  fileId: string,
  status: 'pending' | 'processing' | 'completed' | 'failed',
  progress: number = 0,
  processedPath?: string
): Promise<boolean> {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    const updateData: any = {
      processing_status: status,
      processing_progress: progress,
      updated_at: new Date().toISOString()
    };
    
    if (status === 'completed') {
      updateData.processed_at = new Date().toISOString();
    }
    
    if (processedPath) {
      updateData.processed_path = processedPath;
    }
    
    const { error } = await supabase
      .from('user_files')
      .update(updateData)
      .eq('id', fileId);
    
    if (error) {
      console.error('Update file status error:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Update file status error:', error);
    return false;
  }
}

// Get user's files
export async function getUserFiles(
  userId: string,
  fileType?: 'image' | 'video',
  limit: number = 50,
  offset: number = 0
): Promise<any[] | null> {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    let query = supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (fileType) {
      query = query.eq('file_type', fileType);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Get user files error:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Get user files error:', error);
    return null;
  }
}
