import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { useSupabaseClient } from '@/utils/supabase/client';

// File upload configuration
export const STORAGE_CONFIG = {
  buckets: {
    uploads: 'user-uploads',
    processed: 'processed-content',
    thumbnails: 'thumbnails'
  },
  maxFileSize: {
    image: 50 * 1024 * 1024, // 50MB
    video: 500 * 1024 * 1024, // 500MB
  },
  allowedTypes: {
    image: ['image/jpeg', 'image/png', 'image/webp'],
    video: ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm']
  }
};

// Generate user-specific file path
export function generateFilePath(userId: string, filename: string, type: 'original' | 'processed' | 'thumbnail'): string {
  const timestamp = Date.now();
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');

  switch (type) {
    case 'original':
      return `${userId}/uploads/${timestamp}_${sanitizedFilename}`;
    case 'processed':
      return `${userId}/processed/${timestamp}_processed_${sanitizedFilename}`;
    case 'thumbnail':
      return `${userId}/thumbnails/${timestamp}_thumb_${sanitizedFilename}`;
    default:
      return `${userId}/misc/${timestamp}_${sanitizedFilename}`;
  }
}

// Upload file to Supabase Storage (client-side)
export async function uploadFileClient(
  file: File,
  userId: string,
  supabaseClient: any,
  type: 'original' | 'processed' | 'thumbnail' = 'original'
): Promise<{ path: string; url: string } | null> {
  try {

    // Determine bucket based on type
    let bucket: string;
    switch (type) {
      case 'original':
        bucket = STORAGE_CONFIG.buckets.uploads;
        break;
      case 'processed':
        bucket = STORAGE_CONFIG.buckets.processed;
        break;
      case 'thumbnail':
        bucket = STORAGE_CONFIG.buckets.thumbnails;
        break;
      default:
        bucket = STORAGE_CONFIG.buckets.uploads;
    }

    // Generate file path
    const filePath = generateFilePath(userId, file.name, type);

    // Upload file
    const { data, error } = await supabaseClient.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Upload error:', error);
      return null;
    }

    // Get public URL
    const { data: urlData } = supabaseClient.storage
      .from(bucket)
      .getPublicUrl(filePath);

    return {
      path: filePath,
      url: urlData.publicUrl
    };

  } catch (error) {
    console.error('Upload file error:', error);
    return null;
  }
}

// Get file URL from storage (client-side)
export function getFileUrl(bucket: string, path: string, supabaseClient: any): string | null {
  try {
    const { data } = supabaseClient.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  } catch (error) {
    console.error('Get file URL error:', error);
    return null;
  }
}

// Delete file from storage
export async function deleteFile(bucket: string, path: string): Promise<boolean> {
  try {
    const supabase = createServiceRoleSupabaseClient();

    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      console.error('Delete file error:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Delete file error:', error);
    return false;
  }
}

// Create file record in database
export async function createFileRecord(
  userId: string,
  filename: string,
  filePath: string,
  fileType: 'image' | 'video',
  fileSize: number,
  mimeType: string,
  metadata: any = {}
): Promise<string | null> {
  try {
    const supabase = createServiceRoleSupabaseClient();

    const { data, error } = await supabase
      .from('user_files')
      .insert({
        user_id: userId,
        original_filename: filename,
        file_path: filePath,
        file_type: fileType,
        file_size: fileSize,
        mime_type: mimeType,
        metadata: metadata,
        processing_status: 'pending'
      })
      .select('id')
      .single();

    if (error) {
      console.error('Create file record error:', error);
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Create file record error:', error);
    return null;
  }
}

// Update file processing status
export async function updateFileProcessingStatus(
  fileId: string,
  status: 'pending' | 'processing' | 'completed' | 'failed',
  progress: number = 0,
  processedPath?: string
): Promise<boolean> {
  try {
    const supabase = createServiceRoleSupabaseClient();

    const updateData: any = {
      processing_status: status,
      processing_progress: progress,
      updated_at: new Date().toISOString()
    };

    if (status === 'completed') {
      updateData.processed_at = new Date().toISOString();
    }

    if (processedPath) {
      updateData.processed_path = processedPath;
    }

    const { error } = await supabase
      .from('user_files')
      .update(updateData)
      .eq('id', fileId);

    if (error) {
      console.error('Update file status error:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Update file status error:', error);
    return false;
  }
}

// Get user's files
export async function getUserFiles(
  userId: string,
  fileType?: 'image' | 'video',
  limit: number = 50,
  offset: number = 0
): Promise<any[] | null> {
  try {
    const supabase = createServiceRoleSupabaseClient();

    let query = supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (fileType) {
      query = query.eq('file_type', fileType);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Get user files error:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Get user files error:', error);
    return null;
  }
}

// Validate file type and size
export function validateFile(file: File): { valid: boolean; error?: string } {
  const fileType = file.type.startsWith('image/') ? 'image' : 'video';

  // Check file type
  if (!STORAGE_CONFIG.allowedTypes[fileType].includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not supported. Allowed types: ${STORAGE_CONFIG.allowedTypes[fileType].join(', ')}`
    };
  }

  // Check file size
  if (file.size > STORAGE_CONFIG.maxFileSize[fileType]) {
    const maxSizeMB = STORAGE_CONFIG.maxFileSize[fileType] / (1024 * 1024);
    return {
      valid: false,
      error: `File size exceeds ${maxSizeMB}MB limit for ${fileType} files`
    };
  }

  return { valid: true };
}
