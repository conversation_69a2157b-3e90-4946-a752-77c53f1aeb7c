import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { secureUploadFile } from '@/utils/supabase/secureUpload';
import { createFileRecord } from '@/utils/storage/serverFileManager';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log(`🔒 Secure upload for user ${userId}: ${file.name} (${file.size} bytes)`);

    // Validate file size and type
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File too large' }, { status: 400 });
    }

    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/webp', 'image/gif',
      'video/mp4', 'video/quicktime', 'video/webm'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'File type not allowed' }, { status: 400 });
    }

    // Upload file securely using service role
    const uploadResult = await secureUploadFile(file, userId, 'original');
    if (!uploadResult) {
      return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
    }

    // Determine file type
    const fileType = file.type.startsWith('image/') ? 'image' : 'video';

    // Create file record in database
    const fileId = await createFileRecord(
      userId,
      file.name,
      uploadResult.path,
      fileType,
      file.size,
      file.type,
      {
        originalUrl: uploadResult.url,
        uploadedAt: new Date().toISOString(),
        secureUpload: true
      }
    );

    if (!fileId) {
      return NextResponse.json({ error: 'Failed to create file record' }, { status: 500 });
    }

    console.log(`✅ Secure upload successful: ${fileId}`);

    return NextResponse.json({
      success: true,
      fileId,
      path: uploadResult.path,
      url: uploadResult.url,
      message: 'File uploaded securely'
    });

  } catch (error: any) {
    console.error('❌ Secure upload error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
