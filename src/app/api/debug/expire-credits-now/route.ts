import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

// Manual trigger to expire credits immediately (for testing)
export async function POST() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    console.log('🕐 Manually triggering credit expiration...');

    // Get users with expired credits before cleanup
    const { data: beforeUsers, error: beforeError } = await supabase
      .from('users')
      .select('id, email, credits, credits_expire_at, subscription_type')
      .lt('credits_expire_at', new Date().toISOString())
      .gt('credits', 0);

    console.log('📊 Users with expired credits before cleanup:', beforeUsers?.length || 0);

    // Get expired credit transactions before cleanup
    const { data: beforeTransactions, error: beforeTransError } = await supabase
      .from('credit_transactions')
      .select('id, user_id, amount, expires_at, is_expired')
      .lt('expires_at', new Date().toISOString())
      .eq('is_expired', false)
      .gt('amount', 0);

    console.log('📊 Expired credit transactions before cleanup:', beforeTransactions?.length || 0);

    // Call the database function to expire credits
    const { error: expireError } = await supabase.rpc('expire_credits');

    if (expireError) {
      console.error('❌ Error calling expire_credits function:', expireError);
      throw expireError;
    }

    console.log('✅ expire_credits function executed successfully');

    // Get users after cleanup
    const { data: afterUsers, error: afterError } = await supabase
      .from('users')
      .select('id, email, credits, credits_expire_at, subscription_type')
      .in('id', beforeUsers?.map(u => u.id) || []);

    // Get expired credit transactions after cleanup
    const { data: afterTransactions, error: afterTransError } = await supabase
      .from('credit_transactions')
      .select('id, user_id, amount, expires_at, is_expired')
      .in('id', beforeTransactions?.map(t => t.id) || []);

    // Calculate changes
    const usersUpdated = beforeUsers?.filter(beforeUser => {
      const afterUser = afterUsers?.find(u => u.id === beforeUser.id);
      return afterUser && afterUser.credits !== beforeUser.credits;
    }) || [];

    const transactionsExpired = afterTransactions?.filter(t => t.is_expired) || [];

    console.log(`✅ Credit expiration completed:`);
    console.log(`   - ${usersUpdated.length} users had credits updated`);
    console.log(`   - ${transactionsExpired.length} credit transactions marked as expired`);

    // Detailed logging for debugging
    if (usersUpdated.length > 0) {
      console.log('👥 Users with updated credits:');
      usersUpdated.forEach(user => {
        const afterUser = afterUsers?.find(u => u.id === user.id);
        console.log(`   - ${user.email}: ${user.credits} → ${afterUser?.credits} credits`);
      });
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Credit expiration completed successfully',
      results: {
        usersWithExpiredCredits: beforeUsers?.length || 0,
        expiredTransactions: beforeTransactions?.length || 0,
        usersUpdated: usersUpdated.length,
        transactionsExpired: transactionsExpired.length,
      },
      details: {
        beforeUsers: beforeUsers || [],
        afterUsers: afterUsers || [],
        beforeTransactions: beforeTransactions || [],
        afterTransactions: afterTransactions || [],
        usersUpdated: usersUpdated,
      }
    });

  } catch (error: any) {
    console.error('❌ Error in manual credit expiration:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Also allow GET for easy browser testing
export async function GET() {
  return POST();
}
