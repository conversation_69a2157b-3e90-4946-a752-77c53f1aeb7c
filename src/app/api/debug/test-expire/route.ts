import { NextResponse } from 'next/server';

// Simple test endpoint to verify routing works
export async function GET() {
  console.log('🧪 Test expire endpoint called');
  
  return NextResponse.json({
    success: true,
    message: 'Test expire endpoint is working!',
    timestamp: new Date().toISOString(),
    note: 'If you can see this, the routing is working correctly'
  });
}

export async function POST() {
  return GET();
}
