import { NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

// Check credit expiration status for debugging
export async function GET() {
  try {
    const supabase = createServiceRoleSupabaseClient();

    console.log('🔍 Checking credit expiration status...');

    // Get all users with their credit info
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, credits, credits_expire_at, subscription_type')
      .order('created_at', { ascending: false })
      .limit(20);

    if (usersError) {
      throw usersError;
    }

    // Get credit transactions that should be expired
    const { data: expiredTransactions, error: expiredError } = await supabase
      .from('credit_transactions')
      .select('id, user_id, amount, expires_at, is_expired, description, created_at')
      .lt('expires_at', new Date().toISOString())
      .eq('is_expired', false)
      .gt('amount', 0)
      .order('expires_at', { ascending: true });

    if (expiredError) {
      throw expiredError;
    }

    // Get all credit transactions for analysis
    const { data: allTransactions, error: allTransError } = await supabase
      .from('credit_transactions')
      .select('id, user_id, amount, expires_at, is_expired, description, created_at')
      .order('created_at', { ascending: false })
      .limit(50);

    if (allTransError) {
      throw allTransError;
    }

    // Analyze the data
    const now = new Date();
    const usersWithExpiredCredits = users?.filter(user => 
      user.credits_expire_at && new Date(user.credits_expire_at) < now && user.credits > 0
    ) || [];

    const stats = {
      totalUsers: users?.length || 0,
      usersWithExpiredCredits: usersWithExpiredCredits.length,
      expiredTransactionsNotMarked: expiredTransactions?.length || 0,
      totalTransactions: allTransactions?.length || 0,
      currentTime: now.toISOString(),
    };

    // Group users by subscription type
    const usersByPlan = users?.reduce((acc, user) => {
      const plan = user.subscription_type || 'Unknown';
      if (!acc[plan]) acc[plan] = [];
      acc[plan].push(user);
      return acc;
    }, {} as Record<string, any[]>) || {};

    return NextResponse.json({
      success: true,
      timestamp: now.toISOString(),
      stats,
      usersByPlan,
      usersWithExpiredCredits,
      expiredTransactionsNotMarked: expiredTransactions || [],
      recentTransactions: allTransactions?.slice(0, 10) || [],
      recommendations: [
        expiredTransactions?.length > 0 ? 
          `⚠️ ${expiredTransactions.length} credit transactions should be expired but aren't marked as expired` : 
          '✅ No credit transactions need expiration',
        usersWithExpiredCredits.length > 0 ? 
          `⚠️ ${usersWithExpiredCredits.length} users have expired credits but still show positive balance` : 
          '✅ No users have expired credits with positive balance',
        '💡 Run /api/debug/expire-credits-now to manually trigger expiration',
        '🔄 Automatic expiration runs every 6 hours via Vercel Cron'
      ]
    });

  } catch (error: any) {
    console.error('❌ Error checking credit expiration status:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
