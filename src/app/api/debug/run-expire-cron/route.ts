import { NextResponse } from 'next/server';

// Call the existing cron endpoint directly
export async function GET() {
  try {
    console.log('🔄 Calling existing cron endpoint...');
    
    // Get the base URL
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    
    // Call the existing cron endpoint
    const response = await fetch(`${baseUrl}/api/cron/expire-credits`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add auth header if needed
        ...(process.env.CRON_SECRET_TOKEN && {
          'Authorization': `Bearer ${process.env.CRON_SECRET_TOKEN}`
        })
      }
    });
    
    const result = await response.json();
    
    console.log('✅ Cron endpoint response:', result);
    
    return NextResponse.json({
      success: true,
      message: 'Called existing cron endpoint',
      cronResponse: result,
      cronStatus: response.status,
      timestamp: new Date().toISOString()
    });
    
  } catch (error: any) {
    console.error('❌ Error calling cron endpoint:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST() {
  return GET();
}
