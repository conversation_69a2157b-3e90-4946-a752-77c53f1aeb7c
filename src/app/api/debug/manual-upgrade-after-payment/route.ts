import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    // SECURITY: Only allow in development or with admin access
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (!isDevelopment) {
      return NextResponse.json({
        error: 'This endpoint is only available in development'
      }, { status: 403 });
    }

    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { targetPlan } = await request.json();

    if (!targetPlan || !['standard', 'pro', 'premium'].includes(targetPlan)) {
      return NextResponse.json({
        error: 'Invalid target plan',
        validPlans: ['standard', 'pro', 'premium'],
        usage: 'POST with { "targetPlan": "standard" | "pro" | "premium" }'
      }, { status: 400 });
    }

    console.log(`🚀 Manual upgrade after payment: ${targetPlan} for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get current user data
    const { data: currentUser, error: currentUserError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (currentUserError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch current user data',
        details: currentUserError.message
      }, { status: 500 });
    }

    // Get target plan details
    const { data: targetPlanData, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', targetPlan)
      .single();

    if (planError || !targetPlanData) {
      return NextResponse.json({
        success: false,
        error: `${targetPlan} plan not found in database`,
        details: planError?.message
      }, { status: 500 });
    }

    console.log(`📋 Target plan details:`, targetPlanData);

    // Update user to target plan
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: targetPlanData.name,
        subscription_status: 'active',
        last_payment_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      return NextResponse.json({
        success: false,
        error: `Failed to update user to ${targetPlan} plan`,
        details: userUpdateError.message
      }, { status: 500 });
    }

    // Add plan credits
    let creditsAdded = 0;
    if (targetPlanData.credits_included > 0) {
      // Check if user already has credits for this plan
      const { data: existingCredits } = await supabase
        .from('credit_transactions')
        .select('id')
        .eq('user_id', userId)
        .eq('plan_id', targetPlan)
        .eq('transaction_type', 'subscription')
        .limit(1);

      if (!existingCredits || existingCredits.length === 0) {
        // Add credits with appropriate expiration
        const expiresAt = targetPlanData.credits_expire_days > 0
          ? new Date(Date.now() + targetPlanData.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
          : null;

        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userId,
            amount: targetPlanData.credits_included,
            description: `Manual upgrade after payment: ${targetPlanData.name} plan`,
            transaction_type: 'subscription',
            expires_at: expiresAt,
            plan_id: targetPlan,
          });

        if (creditError) {
          console.error('❌ Error setting credits:', creditError);
        } else {
          // For plan upgrades: SET credits to new plan amount (expire old credits first)
          console.log(`🔄 Plan upgrade: Setting credits to ${targetPlanData.credits_included}`);

          // Expire all existing credits for this user
          await supabase
            .from('credit_transactions')
            .update({ is_expired: true })
            .eq('user_id', userId)
            .eq('is_expired', false);

          // Set user's total credits to the new plan amount
          await supabase
            .from('users')
            .update({
              credits: targetPlanData.credits_included,
              credits_expire_at: expiresAt,
            })
            .eq('id', userId);

          creditsAdded = targetPlanData.credits_included;
          console.log(`✅ Set credits to ${creditsAdded} for user ${userId} (plan upgrade)`);
        }
      } else {
        console.log('⚠️ User already has credits for this plan, updating total only');

        // Just update the total
        const { data: totalCredits, error: totalError } = await supabase
          .from('credit_transactions')
          .select('amount')
          .eq('user_id', userId)
          .eq('is_expired', false);

        if (!totalError) {
          const newTotal = totalCredits?.reduce((sum: number, transaction: any) => sum + (transaction.amount || 0), 0) || 0;

          await supabase
            .from('users')
            .update({ credits: newTotal })
            .eq('id', userId);
        }
      }
    }

    // Record subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: targetPlan,
        action: 'manual_upgrade_after_payment',
        effective_date: new Date().toISOString(),
        metadata: {
          upgrade_method: 'manual_after_payment',
          credits_added: creditsAdded,
          previous_plan: currentUser.subscription_type,
          reason: 'clerk_billing_delay',
        },
      });

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ Manual upgrade completed for user ${userId}: ${targetPlan} plan`);

    return NextResponse.json({
      success: true,
      message: `Successfully upgraded to ${targetPlanData.name} plan`,
      results: {
        previousPlan: currentUser.subscription_type,
        newPlan: targetPlanData.name,
        creditsAdded: creditsAdded,
        totalCredits: updatedUser?.credits || 0,
        user: updatedUser,
      },
      instructions: [
        'Refresh your dashboard to see the updated plan',
        `You now have ${targetPlanData.name} plan with ${targetPlanData.credits_included} credits`,
        'This upgrade will be reflected when Clerk billing updates',
      ],
      note: 'This is a temporary fix while waiting for Clerk billing to update session claims',
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error in manual upgrade after payment:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Manual upgrade after payment endpoint',
    description: 'Manually upgrades user when Clerk billing is delayed',
    usage: 'POST with { "targetPlan": "standard" | "pro" | "premium" }',
    note: 'Use this when payment completed but Clerk session claims not updated yet',
    security: 'Only available in development mode',
    whenToUse: [
      'Payment completed successfully',
      'Clerk still shows u:free_user in session claims',
      'Waiting for Clerk billing to update (can take 5-10 minutes)',
    ],
  });
}
