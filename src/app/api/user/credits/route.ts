import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/utils/supabase/server';
import { auth } from '@clerk/nextjs/server';

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('API: Getting credits for user', userId);

    // Create a Supabase client with the server context
    const supabase = createServerSupabaseClient();

    // Check if user exists
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id, credits, subscription_type')
      .eq('id', userId)
      .single();

    console.log('API: User check result', { existingUser, checkError });

    if (checkError || !existingUser) {
      console.log('API: User not found, creating new user');

      // Get user details from Clerk
      const { data: clerkUser } = await fetch(
        `https://api.clerk.dev/v1/users/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      ).then(res => res.json());

      // Calculate expiration for Free plan (15 days)
      const creditsExpireAt = new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString();

      // Create new user with 50 credits and expiration
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert([
          {
            id: userId,
            email: clerkUser?.email_addresses?.[0]?.email_address || '',
            username: clerkUser?.username || '',
            first_name: clerkUser?.first_name || '',
            last_name: clerkUser?.last_name || '',
            avatar_url: clerkUser?.image_url || '',
            credits: 50,
            subscription_type: 'Free',
            credits_expire_at: creditsExpireAt
          }
        ])
        .select()
        .single();

      console.log('API: New user creation result', { newUser, insertError });

      if (insertError) {
        return NextResponse.json(
          { error: 'Failed to create user', details: insertError },
          { status: 500 }
        );
      }

      // Also create an initial credit transaction
      const { error: transactionError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: 50,
          description: 'Initial signup credits (Free plan)',
          transaction_type: 'bonus',
          expires_at: creditsExpireAt,
          plan_id: 'free',
        });

      if (transactionError) {
        console.error('API: Error creating initial credit transaction:', transactionError);
        // Don't fail the whole request for this
      }

      return NextResponse.json({
        credits: 50,
        subscription_type: 'Free',
        credits_expire_at: creditsExpireAt,
        created: true
      });
    }

    // If credits are 0, update to 50
    if (existingUser.credits === 0) {
      console.log('API: Credits are 0, updating to 50');

      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({ credits: 50 })
        .eq('id', userId)
        .select()
        .single();

      console.log('API: Update result', { updatedUser, updateError });

      if (updateError) {
        return NextResponse.json(
          { error: 'Failed to update credits', details: updateError },
          { status: 500 }
        );
      }

      return NextResponse.json({
        credits: updatedUser.credits,
        subscription_type: updatedUser.subscription_type
      });
    }

    // Return existing user data
    return NextResponse.json({
      credits: existingUser.credits,
      subscription_type: existingUser.subscription_type
    });
  } catch (error) {
    console.error('API: Error in credits endpoint', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}
