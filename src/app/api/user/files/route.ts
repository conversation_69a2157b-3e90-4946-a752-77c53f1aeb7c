import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getUserFiles, createFileRecord, uploadFile, validateFile } from '@/utils/storage/fileManager';

// Get user's files
export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const fileType = searchParams.get('type') as 'image' | 'video' | null;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log(`📁 Fetching files for user ${userId}: type=${fileType}, limit=${limit}, offset=${offset}`);

    const files = await getUserFiles(userId, fileType || undefined, limit, offset);

    if (files === null) {
      return NextResponse.json({ error: 'Failed to fetch files' }, { status: 500 });
    }

    console.log(`✅ Found ${files.length} files for user ${userId}`);

    return NextResponse.json({
      success: true,
      files,
      count: files.length,
      hasMore: files.length === limit
    });

  } catch (error: any) {
    console.error('❌ Error fetching user files:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Upload new file
export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log(`📤 Uploading file for user ${userId}: ${file.name} (${file.size} bytes)`);

    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    // Upload file to storage
    const uploadResult = await uploadFile(file, userId, 'original');
    if (!uploadResult) {
      return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 });
    }

    // Determine file type
    const fileType = file.type.startsWith('image/') ? 'image' : 'video';

    // Create file record in database
    const fileId = await createFileRecord(
      userId,
      file.name,
      uploadResult.path,
      fileType,
      file.size,
      file.type,
      {
        originalUrl: uploadResult.url,
        uploadedAt: new Date().toISOString()
      }
    );

    if (!fileId) {
      return NextResponse.json({ error: 'Failed to create file record' }, { status: 500 });
    }

    console.log(`✅ File uploaded successfully: ${fileId}`);

    return NextResponse.json({
      success: true,
      fileId,
      path: uploadResult.path,
      url: uploadResult.url,
      message: 'File uploaded successfully'
    });

  } catch (error: any) {
    console.error('❌ Error uploading file:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
