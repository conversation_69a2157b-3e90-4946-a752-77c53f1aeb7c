import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getUserFiles, createFileRecord } from '@/utils/storage/serverFileManager';

// Get user's files
export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const fileType = searchParams.get('type') as 'image' | 'video' | null;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log(`📁 Fetching files for user ${userId}: type=${fileType}, limit=${limit}, offset=${offset}`);

    const files = await getUserFiles(userId, fileType || undefined, limit, offset);

    if (files === null) {
      return NextResponse.json({ error: 'Failed to fetch files' }, { status: 500 });
    }

    console.log(`✅ Found ${files.length} files for user ${userId}`);

    return NextResponse.json({
      success: true,
      files,
      count: files.length,
      hasMore: files.length === limit
    });

  } catch (error: any) {
    console.error('❌ Error fetching user files:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Create file record (upload handled client-side)
export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { filename, filePath, fileType, fileSize, mimeType, metadata } = body;

    if (!filename || !filePath || !fileType || !fileSize || !mimeType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log(`📝 Creating file record for user ${userId}: ${filename}`);

    // Create file record in database
    const fileId = await createFileRecord(
      userId,
      filename,
      filePath,
      fileType,
      fileSize,
      mimeType,
      metadata || {}
    );

    if (!fileId) {
      return NextResponse.json({ error: 'Failed to create file record' }, { status: 500 });
    }

    console.log(`✅ File record created successfully: ${fileId}`);

    return NextResponse.json({
      success: true,
      fileId,
      message: 'File record created successfully'
    });

  } catch (error: any) {
    console.error('❌ Error creating file record:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
