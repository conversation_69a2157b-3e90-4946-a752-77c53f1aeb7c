import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Plan configuration
const SUBSCRIPTION_PLANS = {
  standard: {
    name: 'Standard',
    credits: 700,
    monthlyPrice: 7,
    yearlyPrice: 60,
  },
  pro: {
    name: 'Pro',
    credits: 3500,
    monthlyPrice: 26,
    yearlyPrice: 192,
  },
  premium: {
    name: 'Premium',
    credits: 14500,
    monthlyPrice: 78,
    yearlyPrice: 561,
  },
};

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { priceId, planType, isYearly } = await request.json();

    console.log(`💳 Creating subscription checkout session for user ${userId}: ${planType} (${isYearly ? 'yearly' : 'monthly'})`);

    const supabase = createServiceRoleSupabaseClient();

    // Get or create Stripe customer
    let customerId: string;

    // Check if user already has a Stripe customer ID
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('stripe_customer_id, email, first_name, last_name')
      .eq('id', userId)
      .single();

    if (userError || !existingUser) {
      return NextResponse.json({
        error: 'User not found in database'
      }, { status: 404 });
    }

    if (existingUser.stripe_customer_id) {
      customerId = existingUser.stripe_customer_id;
    } else {
      // Create new Stripe customer
      const customer = await stripe.customers.create({
        email: existingUser.email,
        name: `${existingUser.first_name || ''} ${existingUser.last_name || ''}`.trim(),
        metadata: {
          clerk_user_id: userId,
        },
      });

      customerId = customer.id;

      // Update user with Stripe customer ID
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', userId);

      console.log(`✅ Created Stripe customer ${customerId} for user ${userId}`);
    }

    // Get plan details
    const planKey = planType.toLowerCase() as keyof typeof SUBSCRIPTION_PLANS;
    const plan = SUBSCRIPTION_PLANS[planKey];

    if (!plan) {
      return NextResponse.json({
        error: `Invalid plan type: ${planType}`
      }, { status: 400 });
    }

    const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice;
    const interval = isYearly ? 'year' : 'month';

    // Create Stripe checkout session with proper billing descriptor
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `GuardiaVision ${plan.name} Plan`,
              description: `${plan.name} subscription - ${plan.credits} credits per month`,
              metadata: {
                credits: plan.credits.toString(),
                plan_type: planType,
                company: 'GuardiaVision',
              },
            },
            unit_amount: Math.round(price * 100), // Convert to cents
            recurring: {
              interval: interval as 'month' | 'year',
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/dashboard?subscription=success&plan=${planType}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/dashboard?subscription=cancelled`,
      metadata: {
        userId: userId,
        planType: planType,
        credits: plan.credits.toString(),
        type: 'subscription',
        interval: interval,
      },
      // Configure billing descriptor for subscriptions
      subscription_data: {
        description: `GuardiaVision ${plan.name} Plan - ${plan.credits} credits/month`,
        metadata: {
          company: 'GuardiaVision',
          product: 'Subscription',
          plan: plan.name,
          credits: plan.credits.toString(),
          user_id: userId,
        },
      },
      // Additional checkout configuration
      billing_address_collection: 'auto',
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
      allow_promotion_codes: true,
    });

    console.log(`✅ Created Stripe subscription checkout session: ${session.id}`);

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      checkoutUrl: session.url,
      plan: plan,
      message: `Checkout session created for ${plan.name} plan`,
    });

  } catch (error: any) {
    console.error('❌ Error creating subscription checkout session:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Stripe subscription checkout endpoint',
    description: 'Creates Stripe checkout sessions for subscription plans',
    usage: 'POST with { "priceId": "price_id", "planType": "standard|pro|premium", "isYearly": boolean }',
    plans: SUBSCRIPTION_PLANS,
    flow: [
      '1. User selects subscription plan',
      '2. Stripe checkout session created with proper billing descriptor',
      '3. User completes payment',
      '4. Webhook processes subscription and adds credits',
      '5. User redirected back to dashboard with new subscription',
    ],
  });
}
