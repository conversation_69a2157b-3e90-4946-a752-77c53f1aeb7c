import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { NextResponse } from 'next/server';

// Plan-specific expiration days
function getPlanExpirationDays(planId: string): number {
  const expirationMap: Record<string, number> = {
    'free': 15,      // Free plan: 15 days
    'standard': 15,  // Standard plan: 15 days
    'pro': 30,       // Pro plan: 30 days
    'premium': 30,   // Premium plan: 30 days
  };

  return expirationMap[planId] || 30; // Default to 30 days
}

// GET endpoint for testing if the route is deployed
export async function GET() {
  console.log('🔍 GET request received at /api/webhooks/clerk');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Has webhook secret:', !!process.env.CLERK_WEBHOOK_SECRET);

  return NextResponse.json({
    message: 'Clerk webhook endpoint is working',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    hasWebhookSecret: !!process.env.CLERK_WEBHOOK_SECRET,
    url: '/api/webhooks/clerk',
    method: 'GET'
  });
}

export async function POST(req: Request) {
  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // Log the headers for debugging
  console.log('Webhook headers received:', {
    'svix-id': svix_id,
    'svix-timestamp': svix_timestamp,
    'svix-signature': svix_signature ? `${svix_signature.substring(0, 10)}...` : 'missing',
  });

  // If there are no svix headers, return 400
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error('Error: Missing svix headers');
    return new Response('Missing svix headers', { status: 400 });
  }

  // Get the body
  let payload;
  try {
    payload = await req.json();
  } catch (err) {
    console.error('Error parsing request body:', err);
    return new Response('Error parsing request body', { status: 400 });
  }

  const body = JSON.stringify(payload);

  // Log a portion of the body for debugging (be careful not to log sensitive data)
  console.log('Webhook body received (partial):', body.substring(0, 100) + '...');

  // Get the webhook secret from environment variables
  const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

  // Log partial webhook secret for debugging
  console.log('Webhook secret (first few chars):', webhookSecret ? webhookSecret.substring(0, 5) + '...' : 'missing');

  // If there's no webhook secret, return 500
  if (!webhookSecret) {
    console.error('Error: Missing CLERK_WEBHOOK_SECRET');
    return new Response('Missing CLERK_WEBHOOK_SECRET', { status: 500 });
  }

  // Create a new Svix instance
  const wh = new Webhook(webhookSecret);

  let evt: WebhookEvent;

  // Verify the webhook payload
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;

    console.log('Webhook verification successful');
  } catch (err: any) {
    console.error('❌ WEBHOOK VERIFICATION FAILED:', err);
    console.error('Error details:', err.message);

    // Log more details about the verification attempt
    console.log('Verification attempt details:', {
      secretLength: webhookSecret.length,
      bodyLength: body.length,
      headerValues: {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature ? `${svix_signature.substring(0, 10)}...` : 'missing',
      }
    });

    // Return a proper error response that Clerk can understand
    return NextResponse.json(
      {
        error: 'Webhook verification failed',
        message: err.message,
        timestamp: new Date().toISOString()
      },
      { status: 400 }
    );
  }

  // Get the event type
  const eventType = evt.type;
  console.log(`Webhook received: ${eventType}`);

  // Create a Supabase client with service role
  const supabase = createServiceRoleSupabaseClient();

  // Handle different event types
  try {
    switch (eventType) {
      case 'user.created':
        const newUser = evt.data;
        console.log('User created event received:', newUser.id);

        // Calculate expiration for Free plan (15 days)
        const freeExpirationDays = getPlanExpirationDays('free');
        const creditsExpireAt = new Date(Date.now() + freeExpirationDays * 24 * 60 * 60 * 1000).toISOString();

        const { error: createError } = await supabase
          .from('users')
          .insert({
            id: newUser.id,
            email: newUser.email_addresses?.[0]?.email_address || '',
            username: newUser.username || null,
            first_name: newUser.first_name || null,
            last_name: newUser.last_name || null,
            avatar_url: newUser.image_url || null,
            credits: 50,
            subscription_type: 'Free', // Explicitly set the subscription_type
            credits_expire_at: creditsExpireAt, // Set expiration date
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

        if (createError) {
          console.error('Error creating user in Supabase:', createError);
          return NextResponse.json({ success: false, error: createError.message }, { status: 500 });
        }

        console.log(`User created successfully in Supabase with credits expiring in ${freeExpirationDays} days`);

        // Also create an initial credit transaction with expiration
        const { error: transactionError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: newUser.id,
            amount: 50,
            description: 'Initial signup credits (Free plan)',
            transaction_type: 'bonus',
            expires_at: creditsExpireAt, // Set expiration date
            plan_id: 'free',
            created_at: new Date().toISOString(),
          });

        if (transactionError) {
          console.error('Error creating initial credit transaction:', transactionError);
          // Don't fail the whole webhook for this
        } else {
          console.log(`✅ Created initial credit transaction: 50 credits expiring on ${creditsExpireAt}`);
        }

        break;

      case 'user.updated':
        // Update user in Supabase
        const updatedUser = evt.data;
        console.log('🔄 CLERK USER UPDATED WEBHOOK RECEIVED');
        console.log('👤 User ID:', updatedUser.id);
        console.log('📧 Email:', updatedUser.email_addresses?.[0]?.email_address);
        console.log('📋 COMPLETE User update data:', JSON.stringify(updatedUser, null, 2));
        console.log('📋 Public metadata specifically:', JSON.stringify(updatedUser.public_metadata, null, 2));
        console.log('📋 Private metadata specifically:', JSON.stringify(updatedUser.private_metadata, null, 2));
        console.log('📋 Unsafe metadata specifically:', JSON.stringify(updatedUser.unsafe_metadata, null, 2));

        // Check for Stripe billing data in Clerk metadata (following the documentation pattern)
        const hasStripeData = updatedUser.public_metadata?.stripe;
        const hasLegacySubscriptionData = updatedUser.public_metadata?.subscription ||
                                         updatedUser.private_metadata?.subscription ||
                                         updatedUser.unsafe_metadata?.subscription;

        if (hasStripeData || hasLegacySubscriptionData) {
          console.log('💳 STRIPE BILLING DATA DETECTED in user update!');
          console.log('📋 Public metadata:', JSON.stringify(updatedUser.public_metadata, null, 2));
          console.log('📋 Private metadata:', JSON.stringify(updatedUser.private_metadata, null, 2));

          await handleStripeMetadataUpdate(supabase, updatedUser);
        }

        const { error: updateError } = await supabase
          .from('users')
          .update({
            email: updatedUser.email_addresses?.[0]?.email_address || '',
            username: updatedUser.username || null,
            first_name: updatedUser.first_name || null,
            last_name: updatedUser.last_name || null,
            avatar_url: updatedUser.image_url || null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', updatedUser.id);

        if (updateError) {
          console.error('Error updating user in Supabase:', updateError);
          return NextResponse.json({ success: false, error: updateError.message }, { status: 500 });
        }

        console.log('User updated successfully in Supabase');
        break;

      case 'user.deleted':
        // Delete user from Supabase and Stripe
        const deletedUserId = evt.data.id;
        console.log('🗑️ DELETING USER FROM SUPABASE AND STRIPE:', deletedUserId);

        try {
          // First, get user data to find Stripe customer ID
          const { data: userData, error: fetchError } = await supabase
            .from('users')
            .select('stripe_customer_id, email')
            .eq('id', deletedUserId)
            .single();

          if (fetchError && fetchError.code !== 'PGRST116') {
            console.error('Error fetching user data:', fetchError);
          }

          // Cancel Stripe subscription if user has one
          if (userData?.stripe_customer_id) {
            try {
              console.log('🔄 Cancelling Stripe subscriptions for customer:', userData.stripe_customer_id);

              // Import Stripe here to avoid issues if not configured
              const Stripe = require('stripe');
              const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

              // Get all subscriptions for this customer
              const subscriptions = await stripe.subscriptions.list({
                customer: userData.stripe_customer_id,
                status: 'active',
              });

              // Cancel all active subscriptions
              for (const subscription of subscriptions.data) {
                await stripe.subscriptions.cancel(subscription.id);
                console.log('✅ Cancelled Stripe subscription:', subscription.id);
              }

              // Optionally delete the Stripe customer
              // await stripe.customers.del(userData.stripe_customer_id);
              // console.log('✅ Deleted Stripe customer:', userData.stripe_customer_id);

            } catch (stripeError: any) {
              console.error('❌ Error handling Stripe cleanup:', stripeError);
              // Don't fail the whole deletion for Stripe errors
            }
          }

          // Delete user from Supabase (CASCADE DELETE will handle related records)
          console.log('🗑️ Deleting user from Supabase (CASCADE DELETE will handle related records)');
          const { error: deleteError } = await supabase
            .from('users')
            .delete()
            .eq('id', deletedUserId);

          if (deleteError) {
            console.error('❌ Error deleting user from Supabase:', deleteError);
            return NextResponse.json({
              success: false,
              error: `Failed to delete user: ${deleteError.message}`,
              userId: deletedUserId
            }, { status: 500 });
          }

          console.log('✅ USER AND ALL RELATED DATA DELETED SUCCESSFULLY:', deletedUserId);
          console.log('✅ CASCADE DELETE automatically removed all related records');

        } catch (error: any) {
          console.error('❌ CRITICAL ERROR during user deletion:', error);
          return NextResponse.json({
            success: false,
            error: `Critical error during deletion: ${error.message}`,
            userId: deletedUserId
          }, { status: 500 });
        }
        break;

      // Note: user.created, user.updated, user.deleted are already handled above

      // Log all other events to see what Clerk actually sends
      case 'session.created':
      case 'session.ended':
      case 'session.removed':
      case 'session.revoked':
        console.log(`Session event received: ${eventType}`, JSON.stringify(evt.data, null, 2));
        break;

      default:
        console.log(`🔍 UNHANDLED WEBHOOK EVENT: ${eventType}`);
        console.log(`📋 Event data:`, JSON.stringify(evt.data, null, 2));
        console.log(`📊 Full event:`, JSON.stringify(evt, null, 2));

        // Check if this is a billing-related event
        if (eventType.includes('subscription') || eventType.includes('invoice') || eventType.includes('payment')) {
          console.log(`💰 BILLING EVENT DETECTED: ${eventType} - This might need special handling!`);
        }

        // Note: Clerk billing events might have different names
        // We'll handle them through user.updated events with metadata
    }

    console.log(`✅ Webhook processed successfully: ${eventType}`);
    return NextResponse.json({
      success: true,
      eventType,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('❌ Error processing webhook:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      eventType: eventType || 'unknown',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Handle Stripe metadata updates (following the documentation pattern)
async function handleStripeMetadataUpdate(supabase: any, userData: any) {
  console.log('💳 Processing Stripe metadata update for:', userData.id);

  try {
    // Extract Stripe data from public metadata (following documentation pattern)
    const stripeData = userData.public_metadata?.stripe;

    if (!stripeData) {
      console.log('⚠️ No Stripe data found in public metadata');
      return;
    }

    console.log('📋 Stripe metadata:', JSON.stringify(stripeData, null, 2));

    // Check if payment was successful
    if (stripeData.payment === 'paid' && stripeData.plan) {
      console.log(`🎉 Payment successful! Processing ${stripeData.plan} plan for user ${userData.id}`);

      // Map plan to our plan IDs
      const planMapping: Record<string, string> = {
        'standard': 'standard',
        'pro': 'pro',
        'premium': 'premium',
        'Standard': 'standard',
        'Pro': 'pro',
        'Premium': 'premium',
      };

      const planId = planMapping[stripeData.plan] || 'standard';

      // Get plan details from our database
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .single();

      if (planError || !plan) {
        console.error('❌ Plan not found:', planId);
        return;
      }

      // Update user subscription details
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          subscription_type: plan.name,
          subscription_status: stripeData.status || 'active',
          stripe_customer_id: stripeData.customer_id || null,
          stripe_subscription_id: stripeData.subscription_id || null,
          stripe_price_id: stripeData.price_id || null,
          last_payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', userData.id);

      if (userUpdateError) {
        console.error('❌ Error updating user subscription:', userUpdateError);
        throw userUpdateError;
      }

      // Add credits if it's a paid plan
      if (plan.credits_included > 0) {
        const expirationDays = getPlanExpirationDays(planId);
        const expiresAt = expirationDays > 0
          ? new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000).toISOString()
          : null;

        // Insert credit transaction
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userData.id,
            amount: plan.credits_included,
            description: `Subscription: ${plan.name} plan`,
            transaction_type: 'subscription',
            expires_at: expiresAt,
            plan_id: planId,
          });

        if (creditError) {
          console.error('❌ Error adding credits:', creditError);
          throw creditError;
        }

        // Update user's total credits
        const { data: totalCredits, error: totalError } = await supabase
          .from('credit_transactions')
          .select('amount')
          .eq('user_id', userData.id)
          .eq('is_expired', false);

        if (!totalError) {
          const newTotal = totalCredits?.reduce((sum: number, t: any) => sum + t.amount, 0) || 0;

          await supabase
            .from('users')
            .update({
              credits: newTotal,
              credits_expire_at: expiresAt,
            })
            .eq('id', userData.id);
        }

        console.log(`✅ Added ${plan.credits_included} credits to user ${userData.id}`);
      }

      // Record subscription history
      const { error: historyError } = await supabase
        .from('subscription_history')
        .insert({
          user_id: userData.id,
          plan_id: planId,
          action: 'created',
          effective_date: new Date().toISOString(),
          metadata: stripeData,
        });

      if (historyError) {
        console.error('❌ Error recording subscription history:', historyError);
        // Don't throw here as it's not critical
      }

      console.log(`✅ Stripe metadata processed successfully for ${userData.id}: ${planId} plan`);
    } else {
      console.log('⚠️ Payment not completed or no plan specified in Stripe metadata');
    }

  } catch (error) {
    console.error('❌ Error processing Stripe metadata update:', error);
    throw error;
  }
}

// Handle subscription updates from user metadata (legacy)
async function handleSubscriptionUpdateFromUser(supabase: any, userData: any) {
  console.log('💳 Processing subscription update from user metadata for:', userData.id);

  try {
    // Extract subscription data from metadata (multiple possible formats)
    let subscriptionData = userData.public_metadata?.subscription ||
                          userData.private_metadata?.subscription ||
                          userData.unsafe_metadata?.subscription;

    // If no direct subscription object, try to construct from individual fields
    if (!subscriptionData) {
      const planFromMetadata = userData.public_metadata?.plan ||
                              userData.private_metadata?.plan ||
                              userData.unsafe_metadata?.plan;

      if (planFromMetadata) {
        subscriptionData = {
          plan: planFromMetadata,
          status: 'active',
          subscription_id: userData.public_metadata?.subscription_id || userData.private_metadata?.subscription_id,
          customer_id: userData.public_metadata?.stripe_customer_id || userData.private_metadata?.stripe_customer_id,
        };
      }
    }

    if (!subscriptionData) {
      console.log('⚠️ No subscription data found in user metadata');
      return;
    }

    console.log('📋 Subscription metadata:', JSON.stringify(subscriptionData, null, 2));

    // Map Clerk plan to our plan IDs
    const planMapping: Record<string, string> = {
      'standard': 'standard',
      'pro': 'pro',
      'premium': 'premium',
      'Standard': 'standard',
      'Pro': 'pro',
      'Premium': 'premium',
    };

    const planId = planMapping[subscriptionData.plan] || 'standard';

    console.log(`🔄 Mapping plan "${subscriptionData.plan}" to "${planId}"`);

    // Get plan details from our database
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      console.error('❌ Plan not found:', planId);
      return;
    }

    // Update user subscription details
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: plan.name,
        subscription_status: subscriptionData.status || 'active',
        stripe_customer_id: subscriptionData.customer_id || null,
        stripe_subscription_id: subscriptionData.subscription_id || null,
        stripe_price_id: subscriptionData.price_id || null,
        last_payment_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', userData.id);

    if (userUpdateError) {
      console.error('❌ Error updating user subscription:', userUpdateError);
      throw userUpdateError;
    }

    // Handle credits for paid plans
    if (plan.credits_included > 0) {
      const expirationDays = getPlanExpirationDays(planId);
      const expiresAt = expirationDays > 0
        ? new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000).toISOString()
        : null;

      // Check if this is a plan change (upgrade/downgrade) or renewal
      const { data: currentUser, error: currentUserError } = await supabase
        .from('users')
        .select('subscription_type, credits')
        .eq('id', userData.id)
        .single();

      const isNewSubscription = !currentUser?.subscription_type || currentUser.subscription_type === 'Free';
      const isPlanChange = currentUser?.subscription_type && currentUser.subscription_type !== plan.name;

      if (isNewSubscription || isPlanChange) {
        // For new subscriptions or plan changes: SET credits to new plan amount
        console.log(`🔄 ${isNewSubscription ? 'New subscription' : 'Plan change'}: Setting credits to ${plan.credits_included}`);

        // Expire all existing credits for this user
        await supabase
          .from('credit_transactions')
          .update({ is_expired: true })
          .eq('user_id', userData.id)
          .eq('is_expired', false);

        // Insert new credit transaction for the new plan
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userData.id,
            amount: plan.credits_included,
            description: `${isNewSubscription ? 'New subscription' : 'Plan change'}: ${plan.name} plan`,
            transaction_type: 'subscription',
            expires_at: expiresAt,
            plan_id: planId,
          });

        if (creditError) {
          console.error('❌ Error setting credits:', creditError);
          throw creditError;
        }

        // Set user's total credits to the new plan amount
        await supabase
          .from('users')
          .update({
            credits: plan.credits_included,
            credits_expire_at: expiresAt,
          })
          .eq('id', userData.id);

        console.log(`✅ Set credits to ${plan.credits_included} for user ${userData.id} (${isNewSubscription ? 'new subscription' : 'plan change'})`);
      } else {
        // For renewals: ADD credits (existing behavior)
        console.log(`🔄 Subscription renewal: Adding ${plan.credits_included} credits`);

        // Insert credit transaction
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userData.id,
            amount: plan.credits_included,
            description: `Subscription renewal: ${plan.name} plan`,
            transaction_type: 'subscription_renewal',
            expires_at: expiresAt,
            plan_id: planId,
          });

        if (creditError) {
          console.error('❌ Error adding renewal credits:', creditError);
          throw creditError;
        }

        // Update user's total credits by summing all non-expired transactions
        const { data: totalCredits, error: totalError } = await supabase
          .from('credit_transactions')
          .select('amount')
          .eq('user_id', userData.id)
          .eq('is_expired', false);

        if (!totalError) {
          const newTotal = totalCredits?.reduce((sum: number, t: any) => sum + t.amount, 0) || 0;

          await supabase
            .from('users')
            .update({
              credits: newTotal,
              credits_expire_at: expiresAt,
            })
            .eq('id', userData.id);
        }

        console.log(`✅ Added ${plan.credits_included} credits to user ${userData.id} (renewal)`);
      }
    }

    // Record subscription history
    const { error: historyError } = await supabase
      .from('subscription_history')
      .insert({
        user_id: userData.id,
        plan_id: planId,
        action: 'updated',
        effective_date: new Date().toISOString(),
        metadata: subscriptionData,
      });

    if (historyError) {
      console.error('❌ Error recording subscription history:', historyError);
      // Don't throw here as it's not critical
    }

    console.log(`✅ Subscription processed from user metadata for ${userData.id}: ${planId} plan`);

  } catch (error) {
    console.error('❌ Error processing subscription update from user metadata:', error);
    throw error;
  }
}

// Subscription event handlers
async function handleSubscriptionCreated(supabase: any, data: any) {
  const { id: user_id, subscription } = data;
  const { plan_id, status, current_period_end } = subscription;
  console.log('Subscription created:', { user_id, plan_id, status });

  // Map Clerk plan IDs to your subscription types
  const subscriptionTypeMap: Record<string, { type: string; credits: number }> = {
    'standard': { type: 'Standard', credits: 700 },
    'pro': { type: 'Pro', credits: 999999 }, // Unlimited
    'premium': { type: 'Premium', credits: 999999 }, // Unlimited
  };

  const planInfo = subscriptionTypeMap[plan_id];

  if (!planInfo) {
    console.error(`Unknown plan ID: ${plan_id}`);
    return;
  }

  const { error } = await supabase
    .from('users')
    .update({
      subscription_type: planInfo.type,
      credits: planInfo.credits,
      subscription_status: status,
      clerk_plan_id: plan_id,
      subscription_period_end: current_period_end ? new Date(current_period_end * 1000).toISOString() : null,
      updated_at: new Date().toISOString(),
    })
    .eq('id', user_id);

  if (error) {
    console.error('Error updating subscription:', error);
    throw error;
  }

  // Log the subscription creation
  await supabase
    .from('credit_transactions')
    .insert({
      user_id,
      amount: planInfo.credits,
      transaction_type: 'subscription_renewal',
      description: `Subscription created: ${planInfo.type}`,
      created_at: new Date().toISOString(),
    });

  console.log(`Subscription created successfully for user ${user_id}`);
}

async function handleSubscriptionUpdated(supabase: any, data: any) {
  const { id: user_id, subscription } = data;
  const { plan_id, status, current_period_end } = subscription;
  console.log('Subscription updated:', { user_id, plan_id, status });

  const subscriptionTypeMap: Record<string, { type: string; credits: number }> = {
    'standard': { type: 'Standard', credits: 700 },
    'pro': { type: 'Pro', credits: 999999 },
    'premium': { type: 'Premium', credits: 999999 },
  };

  const planInfo = subscriptionTypeMap[plan_id];

  if (!planInfo) {
    console.error(`Unknown plan ID: ${plan_id}`);
    return;
  }

  const { error } = await supabase
    .from('users')
    .update({
      subscription_type: planInfo.type,
      credits: planInfo.credits,
      subscription_status: status,
      clerk_plan_id: plan_id,
      subscription_period_end: current_period_end ? new Date(current_period_end * 1000).toISOString() : null,
      updated_at: new Date().toISOString(),
    })
    .eq('id', user_id);

  if (error) {
    console.error('Error updating subscription:', error);
    throw error;
  }

  console.log(`Subscription updated successfully for user ${user_id}`);
}

async function handleSubscriptionCancelled(supabase: any, data: any) {
  const { id: user_id } = data;
  console.log('Subscription cancelled for user:', user_id);

  // When subscription is cancelled, revert to Free plan
  const { error } = await supabase
    .from('users')
    .update({
      subscription_type: 'Free',
      credits: 50,
      subscription_status: 'cancelled',
      clerk_plan_id: null,
      subscription_period_end: null,
      updated_at: new Date().toISOString(),
    })
    .eq('id', user_id);

  if (error) {
    console.error('Error cancelling subscription:', error);
    throw error;
  }

  // Log the cancellation
  await supabase
    .from('credit_transactions')
    .insert({
      user_id,
      amount: 50,
      transaction_type: 'subscription_cancelled',
      description: 'Subscription cancelled - reverted to Free plan',
      created_at: new Date().toISOString(),
    });

  console.log(`Subscription cancelled successfully for user ${user_id}`);
}

async function handleInvoicePaid(supabase: any, data: any) {
  const { user_id, plan_id, amount_paid } = data;
  console.log('Invoice paid:', { user_id, plan_id, amount_paid });

  // Log the payment
  await supabase
    .from('credit_transactions')
    .insert({
      user_id,
      amount: 0, // This is a payment, not credits
      transaction_type: 'payment',
      description: `Payment received for ${plan_id}: $${(amount_paid / 100).toFixed(2)}`,
      created_at: new Date().toISOString(),
    });

  console.log(`Payment successful for user ${user_id}: $${(amount_paid / 100).toFixed(2)}`);
}

async function handlePaymentFailed(supabase: any, data: any) {
  const { user_id, plan_id, amount_due } = data;
  console.log('Payment failed:', { user_id, plan_id, amount_due });

  // Log the failed payment
  await supabase
    .from('credit_transactions')
    .insert({
      user_id,
      amount: 0,
      transaction_type: 'payment_failed',
      description: `Payment failed for ${plan_id}: $${(amount_due / 100).toFixed(2)}`,
      created_at: new Date().toISOString(),
    });

  console.log(`Payment failed for user ${user_id}: $${(amount_due / 100).toFixed(2)}`);
}
