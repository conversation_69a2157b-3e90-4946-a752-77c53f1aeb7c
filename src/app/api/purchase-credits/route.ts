import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Credit packages available for purchase
const CREDIT_PACKAGES = {
  small: {
    id: 'small',
    name: '500 Credits',
    credits: 500,
    price: 5.00, // $5
    description: 'Perfect for occasional use',
    popular: false,
  },
  medium: {
    id: 'medium',
    name: '1,200 Credits',
    credits: 1200,
    price: 10.00, // $10 (20% bonus)
    description: 'Great value for regular users',
    popular: true,
  },
  large: {
    id: 'large',
    name: '3,000 Credits',
    credits: 3000,
    price: 20.00, // $20 (50% bonus)
    description: 'Best for heavy usage',
    popular: false,
  },
  xlarge: {
    id: 'xlarge',
    name: '7,500 Credits',
    credits: 7500,
    price: 40.00, // $40 (87.5% bonus)
    description: 'Maximum value pack',
    popular: false,
  },
};

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { packageId } = await request.json();

    if (!packageId || !CREDIT_PACKAGES[packageId as keyof typeof CREDIT_PACKAGES]) {
      return NextResponse.json({
        error: 'Invalid package ID',
        availablePackages: Object.keys(CREDIT_PACKAGES)
      }, { status: 400 });
    }

    const package_ = CREDIT_PACKAGES[packageId as keyof typeof CREDIT_PACKAGES];

    console.log(`💳 Creating credit purchase session for user ${userId}: ${package_.name}`);

    const supabase = createServiceRoleSupabaseClient();

    // Get or create Stripe customer
    let customerId: string;

    // Check if user already has a Stripe customer ID
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('stripe_customer_id, email')
      .eq('id', userId)
      .single();

    if (userError || !existingUser) {
      return NextResponse.json({
        error: 'User not found in database'
      }, { status: 404 });
    }

    if (existingUser.stripe_customer_id) {
      customerId = existingUser.stripe_customer_id;
    } else {
      // Create new Stripe customer
      const customer = await stripe.customers.create({
        email: existingUser.email,
        metadata: {
          clerk_user_id: userId,
        },
      });

      customerId = customer.id;

      // Update user with Stripe customer ID
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', userId);

      console.log(`✅ Created Stripe customer ${customerId} for user ${userId}`);
    }

    // Create Stripe checkout session with proper billing descriptor
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `GuardiaVision - ${package_.name}`,
              description: `${package_.description} - ${package_.credits} credits`,
              metadata: {
                credits: package_.credits.toString(),
                package_id: packageId,
                company: 'GuardiaVision',
              },
            },
            unit_amount: Math.round(package_.price * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/dashboard?credits_purchased=true&package=${packageId}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/dashboard?credits_cancelled=true`,
      metadata: {
        userId: userId,
        packageId: packageId,
        credits: package_.credits.toString(),
        type: 'credit_purchase',
      },
      // Configure billing descriptor with immediate capture
      payment_intent_data: {
        statement_descriptor: 'GUARDIAVISION',
        statement_descriptor_suffix: 'CREDITS',
        description: `GuardiaVision Credits - ${package_.name} (${package_.credits} credits)`,
        capture_method: 'automatic', // Immediate capture
        confirmation_method: 'automatic', // Immediate confirmation
        metadata: {
          company: 'GuardiaVision',
          product: 'Credits',
          package: package_.name,
          credits: package_.credits.toString(),
          user_id: userId,
        },
      },
      // Additional checkout configuration
      billing_address_collection: 'auto',
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
    });

    console.log(`✅ Created Stripe checkout session: ${session.id}`);

    return NextResponse.json({
      success: true,
      checkoutUrl: session.url,
      sessionId: session.id,
      package: package_,
      message: `Checkout session created for ${package_.name}`,
    });

  } catch (error: any) {
    console.error('❌ Error creating credit purchase session:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Credit purchase endpoint',
    description: 'Creates Stripe checkout sessions for purchasing additional credits',
    usage: 'POST with { "packageId": "small" | "medium" | "large" | "xlarge" }',
    packages: CREDIT_PACKAGES,
    flow: [
      '1. User selects credit package',
      '2. Stripe checkout session created',
      '3. User completes payment',
      '4. Webhook processes payment and adds credits',
      '5. User redirected back to dashboard with new credits',
    ],
  });
}
