'use client';

import { SimpleFileUpload } from '@/components/debug/SimpleFileUpload';
import { PublicUploadTest } from '@/components/debug/PublicUploadTest';
import { AnonymousUploadTest } from '@/components/debug/AnonymousUploadTest';
import { SecureUploadTest } from '@/components/debug/SecureUploadTest';
import { UserFilesManager } from '@/components/dashboard/UserFilesManager';
import { ErrorBoundary } from '@/components/debug/ErrorBoundary';

export default function StorageTestPage() {
  return (
    <div className="p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-bold text-white mb-2">Storage System Debug</h1>
        <p className="text-gray-400">
          Debug the file storage system step by step.
        </p>
      </div>

      {/* Anonymous Upload Test */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4">Step 1: Anonymous Upload Test</h2>
        <ErrorBoundary>
          <AnonymousUploadTest />
        </ErrorBoundary>
      </div>

      {/* Public Upload Test */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4">Step 2: Public Upload Test</h2>
        <ErrorBoundary>
          <PublicUploadTest />
        </ErrorBoundary>
      </div>

      {/* Private Upload Test */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4">Step 3: Private Upload Test</h2>
        <ErrorBoundary>
          <SimpleFileUpload />
        </ErrorBoundary>
      </div>

      {/* Secure Upload Test */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4">Step 4: Secure Upload Test (RLS)</h2>
        <ErrorBoundary>
          <SecureUploadTest />
        </ErrorBoundary>
      </div>

      {/* Full File Manager */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4">Step 5: Full File Manager</h2>
        <ErrorBoundary>
          <UserFilesManager showUpload={true} />
        </ErrorBoundary>
      </div>
    </div>
  );
}
