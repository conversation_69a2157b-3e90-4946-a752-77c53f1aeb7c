{"name": "saas-boilerplate", "version": "1.7.4", "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev", "dev": "run-p dev:*", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook"}, "dependencies": {"@clerk/localizations": "^3.16.1", "@clerk/nextjs": "^6.20.0", "@clerk/themes": "^2.2.46", "@electric-sql/pglite": "^0.2.12", "@hookform/resolvers": "^3.9.0", "@logtail/pino": "^0.5.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.3", "@sendgrid/mail": "^8.1.5", "@sentry/nextjs": "^8.34.0", "@spotlightjs/spotlight": "^2.5.0", "@stripe/stripe-js": "^7.3.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.9", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.35.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.7.4", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "next": "^14.2.25", "next-intl": "^3.21.1", "next-themes": "^0.3.0", "pg": "^8.13.0", "pino": "^9.5.0", "pino-pretty": "^11.3.0", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-day-picker": "^9.6.6", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "sharp": "^0.33.5", "sonner": "^2.0.3", "stripe": "^16.12.0", "svix": "^1.65.0", "tailwind-merge": "^2.6.0", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@clerk/testing": "^1.7.3", "@faker-js/faker": "^9.0.3", "@next/bundle-analyzer": "^14.2.15", "@percy/cli": "1.30.1", "@percy/playwright": "^1.0.6", "@playwright/test": "^1.48.1", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-interactions": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/addon-onboarding": "^8.3.5", "@storybook/blocks": "^8.3.5", "@storybook/nextjs": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/test": "^8.3.5", "@storybook/test-runner": "^0.19.1", "@testing-library/jest-dom": "^6.6.1", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.7.6", "@types/pg": "^8.11.10", "@types/react": "^18.3.11", "@vitejs/plugin-react": "^4.3.2", "@vitest/coverage-v8": "^2.1.9", "@vitest/expect": "^2.1.9", "autoprefixer": "^10.4.20", "checkly": "^4.9.0", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "dotenv-cli": "^7.4.2", "drizzle-kit": "^0.26.2", "eslint": "^8.57.0", "eslint-config-next": "^14.2.25", "http-server": "^14.1.1", "jiti": "^1.21.6", "jsdom": "^25.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "rimraf": "^6.0.1", "start-server-and-test": "^2.0.8", "storybook": "^8.3.5", "supabase": "^2.22.12", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.1", "typescript": "^5.6.3", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.1.9", "vitest-fail-on-console": "^0.7.1"}, "release": {"branches": ["main"]}}