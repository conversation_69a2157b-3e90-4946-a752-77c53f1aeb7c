-- =====================================================
-- DATABASE SETUP FOR GUARDIAVISION (No Storage Policies)
-- =====================================================

-- 1. Create user_files table to track file metadata
CREATE TABLE IF NOT EXISTS user_files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  original_filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  processed_path TEXT,
  thumbnail_path TEXT,
  file_type TEXT NOT NULL, -- 'image' or 'video'
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  processing_status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  processing_progress INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);

-- 2. RLS policies for user_files table
ALTER TABLE user_files ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own files" ON user_files
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own files" ON user_files
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own files" ON user_files
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own files" ON user_files
  FOR DELETE USING (auth.uid()::text = user_id);

-- 3. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_files_user_id ON user_files(user_id);
CREATE INDEX IF NOT EXISTS idx_user_files_created_at ON user_files(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_files_processing_status ON user_files(processing_status);
CREATE INDEX IF NOT EXISTS idx_user_files_file_type ON user_files(file_type);

-- 4. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 5. Create trigger for updated_at
DROP TRIGGER IF EXISTS update_user_files_updated_at ON user_files;
CREATE TRIGGER update_user_files_updated_at 
  BEFORE UPDATE ON user_files 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 6. Create function to clean up storage when file record is deleted
-- Note: This function will be created but storage cleanup will be handled in application code
CREATE OR REPLACE FUNCTION cleanup_file_storage()
RETURNS TRIGGER AS $$
BEGIN
  -- Storage cleanup will be handled by application code
  -- This function is a placeholder for future server-side cleanup
  RETURN OLD;
END;
$$ language 'plpgsql';

-- 7. Create trigger for storage cleanup
DROP TRIGGER IF EXISTS cleanup_file_storage_trigger ON user_files;
CREATE TRIGGER cleanup_file_storage_trigger
  AFTER DELETE ON user_files
  FOR EACH ROW
  EXECUTE FUNCTION cleanup_file_storage();
