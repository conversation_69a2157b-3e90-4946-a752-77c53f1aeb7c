-- =====================================================
-- SUPABASE STORAGE SETUP FOR GUARDIAVISION
-- =====================================================

-- 1. Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('user-uploads', 'user-uploads', false, 104857600, ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/quicktime', 'video/x-msvideo']),
  ('processed-content', 'processed-content', false, 104857600, ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/quicktime', 'video/x-msvideo']),
  ('thumbnails', 'thumbnails', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']);

-- 2. Storage policies for user-uploads bucket
CREATE POLICY "Users can upload to their own folder" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'user-uploads' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own uploads" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'user-uploads' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own uploads" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'user-uploads' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own uploads" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'user-uploads' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- 3. Storage policies for processed-content bucket
CREATE POLICY "Users can view their own processed content" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'processed-content' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload processed content to their folder" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'processed-content' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own processed content" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'processed-content' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own processed content" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'processed-content' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- 4. Storage policies for thumbnails bucket (public read)
CREATE POLICY "Anyone can view thumbnails" ON storage.objects
  FOR SELECT USING (bucket_id = 'thumbnails');

CREATE POLICY "Users can upload thumbnails to their folder" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'thumbnails' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- 5. Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 6. Create user_files table to track file metadata
CREATE TABLE IF NOT EXISTS user_files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  original_filename TEXT NOT NULL,
  file_path TEXT NOT NULL,
  processed_path TEXT,
  thumbnail_path TEXT,
  file_type TEXT NOT NULL, -- 'image' or 'video'
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  processing_status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  processing_progress INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);

-- 7. RLS policies for user_files table
ALTER TABLE user_files ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own files" ON user_files
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own files" ON user_files
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own files" ON user_files
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own files" ON user_files
  FOR DELETE USING (auth.uid()::text = user_id);

-- 8. Create indexes for performance
CREATE INDEX idx_user_files_user_id ON user_files(user_id);
CREATE INDEX idx_user_files_created_at ON user_files(created_at DESC);
CREATE INDEX idx_user_files_processing_status ON user_files(processing_status);
CREATE INDEX idx_user_files_file_type ON user_files(file_type);

-- 9. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 10. Create trigger for updated_at
CREATE TRIGGER update_user_files_updated_at 
  BEFORE UPDATE ON user_files 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 11. Create function to clean up storage when file record is deleted
CREATE OR REPLACE FUNCTION cleanup_file_storage()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete original file
  IF OLD.file_path IS NOT NULL THEN
    PERFORM storage.delete_object('user-uploads', OLD.file_path);
  END IF;
  
  -- Delete processed file
  IF OLD.processed_path IS NOT NULL THEN
    PERFORM storage.delete_object('processed-content', OLD.processed_path);
  END IF;
  
  -- Delete thumbnail
  IF OLD.thumbnail_path IS NOT NULL THEN
    PERFORM storage.delete_object('thumbnails', OLD.thumbnail_path);
  END IF;
  
  RETURN OLD;
END;
$$ language 'plpgsql';

-- 12. Create trigger for storage cleanup
CREATE TRIGGER cleanup_file_storage_trigger
  AFTER DELETE ON user_files
  FOR EACH ROW
  EXECUTE FUNCTION cleanup_file_storage();
