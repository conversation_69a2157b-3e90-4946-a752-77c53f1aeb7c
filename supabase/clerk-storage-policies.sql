-- =====================================================
-- SECURE CLERK + SUPABASE STORAGE POLICIES
-- =====================================================

-- First, re-enable RLS if it was disabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "authenticated_uploads" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload to their own folder" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own uploads" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own uploads" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own uploads" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own processed content" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload processed content to their folder" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload thumbnails to their folder" ON storage.objects;

-- Create Clerk-compatible policies for user-uploads bucket
-- Use auth.uid() which should work with Clerk's standard sub claim
CREATE POLICY "clerk_user_uploads_select" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'user-uploads'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "clerk_user_uploads_insert" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'user-uploads'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "clerk_user_uploads_update" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'user-uploads'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "clerk_user_uploads_delete" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'user-uploads'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

-- Create Clerk-compatible policies for processed-content bucket
CREATE POLICY "clerk_processed_select" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'processed-content'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

CREATE POLICY "clerk_processed_insert" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'processed-content'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

CREATE POLICY "clerk_processed_update" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'processed-content'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

CREATE POLICY "clerk_processed_delete" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'processed-content'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

-- Create policies for thumbnails bucket (public read, user write)
CREATE POLICY "thumbnails_public_select" ON storage.objects
  FOR SELECT USING (bucket_id = 'thumbnails');

CREATE POLICY "clerk_thumbnails_insert" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'thumbnails'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

CREATE POLICY "clerk_thumbnails_update" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'thumbnails'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

CREATE POLICY "clerk_thumbnails_delete" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'thumbnails'
    AND (storage.foldername(name))[1] = (auth.jwt() ->> 'sub')
  );

-- Update user_files table policies for Clerk
ALTER TABLE user_files ENABLE ROW LEVEL SECURITY;

-- Drop existing user_files policies
DROP POLICY IF EXISTS "Users can view their own files" ON user_files;
DROP POLICY IF EXISTS "Users can insert their own files" ON user_files;
DROP POLICY IF EXISTS "Users can update their own files" ON user_files;
DROP POLICY IF EXISTS "Users can delete their own files" ON user_files;

-- Create Clerk-compatible policies for user_files
CREATE POLICY "clerk_user_files_select" ON user_files
  FOR SELECT USING (user_id = (auth.jwt() ->> 'sub'));

CREATE POLICY "clerk_user_files_insert" ON user_files
  FOR INSERT WITH CHECK (user_id = (auth.jwt() ->> 'sub'));

CREATE POLICY "clerk_user_files_update" ON user_files
  FOR UPDATE USING (user_id = (auth.jwt() ->> 'sub'));

CREATE POLICY "clerk_user_files_delete" ON user_files
  FOR DELETE USING (user_id = (auth.jwt() ->> 'sub'));

-- Create function to get current Clerk user ID
CREATE OR REPLACE FUNCTION get_clerk_user_id()
RETURNS TEXT AS $$
BEGIN
  RETURN auth.jwt() ->> 'sub';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
